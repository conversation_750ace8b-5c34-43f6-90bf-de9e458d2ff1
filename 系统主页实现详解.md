# CodeDuel 系统主页实现详解

## 系统架构概述

CodeDuel 主页采用现代化的前后端分离架构，提供丰富的数据展示和快速导航功能：

```
前端主页组件 ←→ Dashboard布局 ←→ 后端API ←→ 数据库
     ↓              ↓           ↓        ↓
  数据展示      导航菜单      统计接口   多表查询
```

## 技术栈

- **前端**: Vue 3 + Element Plus + SCSS + ECharts
- **后端**: Spring Boot + MyBatis Plus + MySQL
- **状态管理**: Pinia (持久化存储)
- **样式**: SCSS + 响应式设计

## 主页核心功能

### 1. 系统主页布局 (`Dashboard.vue`)

#### 顶部导航栏实现

```vue
<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <!-- 左侧Logo -->
      <div class="logo-section">
        <CodeDuelLogo :width="170" :height="45" :show-subtitle="false" />
      </div>

      <!-- 中间导航菜单 -->
      <div class="nav-section">
        <el-menu
            :default-active="activeIndex"
            class="nav-menu"
            mode="horizontal"
            @select="handleSelect"
            background-color="transparent"
            text-color="#333"
            active-text-color="#409EFF">
          <!-- 首页 -->
          <el-menu-item index="dashboard/home">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          <!-- 对战 -->
          <el-menu-item index="dashboard/battle">
            <el-icon><Promotion /></el-icon>
            <span>对战</span>
          </el-menu-item>
          <!-- 排行榜 -->
          <el-menu-item index="dashboard/ranking">
            <el-icon><Trophy /></el-icon>
            <span>排行榜</span>
          </el-menu-item>
          <!-- 讨论区 -->
          <el-menu-item index="dashboard/forum">
            <el-icon><ChatDotRound /></el-icon>
            <span>讨论区</span>
          </el-menu-item>
          <!-- 聊天室 -->
          <el-menu-item index="dashboard/chat">
            <el-icon><ChatLineRound /></el-icon>
            <span>聊天室</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧用户区域 -->
      <div class="user-section">
        <el-dropdown placement="bottom-end" @command="handleCommand">
          <span class="user-dropdown">
            <el-avatar
                :size="40"
                :src="userInfoStore.userInfo.avatar ? userInfoStore.userInfo.avatar : avatar"
                class="user-avatar"
            />
            <el-icon class="dropdown-icon">
              <CaretBottom/>
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="info" :icon="User">基本资料</el-dropdown-item>
              <el-dropdown-item command="admin" :icon="Setting" v-if="userInfoStore.userInfo.isAdmin">管理后台</el-dropdown-item>
              <el-dropdown-item command="logout" :icon="SwitchButton" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主内容区域 -->
    <el-main class="main-content">
      <router-view/>
    </el-main>
  </div>
</template>
```

#### 导航逻辑实现

```javascript
<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserInfoStore } from "@/stores/userInfo"
import { ElMessage, ElMessageBox } from "element-plus"

const userInfoStore = useUserInfoStore()
const router = useRouter()
const activeIndex = ref('home')

// 处理菜单选择
const handleSelect = (key) => {
  activeIndex.value = key
  router.push('/' + key)
}

// 处理用户下拉菜单命令
const handleCommand = (command) => {
  if (command === 'logout') {
    // 退出登录确认
    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 清除用户信息
      userInfoStore.removeUserInfo()
      ElMessage.success('退出登录成功')
      router.push('/login')
    })
  } else if (command === 'admin') {
    // 跳转到管理后台
    router.push('/admin/userlist')
  } else if (command === 'info') {
    // 跳转到基本资料页面
    router.push('/dashboard/user/info')
  }
}
</script>
```

### 2. 主页内容实现 (`Home.vue`)

#### 欢迎横幅区域

```vue
<template>
  <div class="home-container">
    <!-- 系统主页欢迎横幅 -->
    <el-card class="welcome-banner" shadow="hover">
      <div class="banner-content">
        <h1>欢迎来到 CodeDuel</h1>
        <p>在这里挑战自己，与全球程序员一较高下！</p>
        <el-button type="primary" size="large" @click="startBattle">
          <el-icon><Promotion /></el-icon>
          开始对战
        </el-button>
      </div>
    </el-card>

    <!-- 统计数据展示区域 -->
    <el-row :gutter="20" class="stats-row">
      <!-- 注册用户统计 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><User /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">注册用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 总对战数统计 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><Trophy /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalBattles }}</div>
              <div class="stat-label">总对战数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 题目总数统计 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><Document /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalProblems }}</div>
              <div class="stat-label">题目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 发帖总数统计 -->
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <el-icon class="stat-icon"><ChatDotRound /></el-icon>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalPosts }}</div>
              <div class="stat-label">发帖总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
```

#### 主要功能区域 - 三栏布局

```vue
<!-- 主要功能区域 - 三等分布局 -->
<el-row :gutter="20" class="main-features">
  <!-- 排行榜预览 -->
  <el-col :span="8">
    <el-card class="feature-card" header="排行榜 Top 10">
      <div class="ranking-preview">
        <div v-for="(user, index) in topRanking" :key="user.id" class="ranking-item">
          <div class="rank-number">{{ index + 1 }}</div>
          <el-avatar :size="30" :src="user.avatar || '/default-avatar.png'" />
          <div class="user-info">
            <div class="username">{{ user.codeforcesId }}</div>
            <div class="rating">Rating: {{ user.rating }} ({{ user.totalBattles }}场)</div>
          </div>
        </div>
      </div>
      <div class="view-more">
        <el-button type="text" @click="viewRanking">查看完整排行榜</el-button>
      </div>
    </el-card>
  </el-col>

  <!-- 最近对战记录 -->
  <el-col :span="8">
    <el-card class="feature-card" header="最近对战">
      <div class="battle-history">
        <div v-for="battle in recentBattles" :key="battle.battleRecord.id" class="battle-item">
          <div class="battle-info">
            <div class="battle-title">{{ battle.problem?.title || '未知题目' }}</div>
            <div class="battle-time">{{ formatTime(battle.battleRecord.startTime) }}</div>
            <div class="battle-participants">
              参与者: {{ battle.participants?.map(p => p.codeforcesId).join(', ') || '无' }}
            </div>
          </div>
          <div class="battle-result">
            <el-tag :type="battle.battleRecord.isRoom ? 'warning' : 'info'">
              {{ battle.battleRecord.isRoom ? '房间对战' : '匹配对战' }}
            </el-tag>
          </div>
        </div>
      </div>
      <div class="view-more">
        <el-button type="text" @click="viewBattleHistory">查看更多记录</el-button>
      </div>
    </el-card>
  </el-col>

  <!-- 最新发帖 -->
  <el-col :span="8">
    <el-card class="feature-card" header="最新发帖">
      <div class="forum-preview">
        <div v-for="post in hotPosts" :key="post.post.id" class="post-item">
          <div class="post-content">
            <div class="post-title">{{ post.post.title }}</div>
            <div class="post-meta">
              <span>{{ post.user?.codeforcesId || '匿名用户' }}</span>
              <span>{{ formatTime(post.post.postTime) }}</span>
              <span>{{ post.post.commentCount || 0 }} 评论</span>
              <span v-if="post.post.isTop">🔝 置顶</span>
            </div>
          </div>
          <el-button type="text" size="small" @click="viewPost(post.post.id)">查看</el-button>
        </div>
      </div>
      <div class="view-more">
        <el-button type="text" @click="viewForum">进入讨论区</el-button>
      </div>
    </el-card>
  </el-col>
</el-row>
```

#### 数据加载逻辑实现

```javascript
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getHomeStats, getUserList, getRecentBattleRecords, getPagePosts } from '@/api/api'

const router = useRouter()

// 响应式数据定义
const stats = ref({
  totalUsers: 0,      // 总用户数
  totalBattles: 0,    // 总对战数
  totalProblems: 0,   // 题目总数
  totalPosts: 0       // 发帖总数
})

const topRanking = ref([])      // 排行榜数据
const recentBattles = ref([])   // 最近对战记录
const hotPosts = ref([])        // 热门帖子
const loading = ref(false)      // 加载状态

// 页面导航方法
const startBattle = () => {
  router.push('/dashboard/battle')
}

const viewRanking = () => {
  router.push('/dashboard/ranking')
}

const viewBattleHistory = () => {
  router.push('/user/info')
}

const viewForum = () => {
  router.push('/dashboard/forum')
}

const viewPost = (postId) => {
  router.push(`/forum/post/${postId}`)
}

// 时间格式化工具函数
const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await getHomeStats()
    if (response.status) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

// 加载排行榜前10名
const loadTopRanking = async () => {
  try {
    const response = await getUserList({ page: 1, size: 10, username: '' })
    if (response.status && response.data) {
      topRanking.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载排行榜失败:', error)
    ElMessage.error('加载排行榜失败')
  }
}

// 加载最近对战记录
const loadRecentBattles = async () => {
  try {
    const response = await getRecentBattleRecords({ page: 1, size: 10 })
    if (response.status && response.data) {
      recentBattles.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载对战记录失败:', error)
    ElMessage.error('加载对战记录失败')
  }
}

// 加载热门帖子
const loadHotPosts = async () => {
  try {
    const response = await getPagePosts({ pageNo: 1, pageSize: 10 })
    if (response.status && response.data) {
      hotPosts.value = response.data.records || []
    }
  } catch (error) {
    console.error('加载帖子失败:', error)
    ElMessage.error('加载帖子失败')
  }
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    // 并行加载所有数据，提高页面加载速度
    await Promise.all([
      loadStats(),
      loadTopRanking(),
      loadRecentBattles(),
      loadHotPosts()
    ])
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAllData()
})
</script>
```

### 3. 后端API实现

#### 统计数据接口 (`UsersController.java`)

```java
/**
 * 获取首页统计数据
 * 功能：提供系统总体数据统计，包括用户数、对战数、题目数、帖子数
 */
@GetMapping("/home-stats")
@ResponseBody
public JsonResponse getHomeStats() {
    try {
        // 调用服务层获取统计数据
        Map<String, Object> stats = usersService.getHomeStats();
        return JsonResponse.success(stats);
    } catch (Exception e) {
        return JsonResponse.failure("获取统计数据失败: " + e.getMessage());
    }
}

/**
 * 获取用户列表（仅基本信息 + 对战记录条数，用于排行榜）
 * 功能：分页查询用户列表，支持用户名搜索，包含对战统计信息
 */
@GetMapping("list")
@ResponseBody
public JsonResponse getUsersList(
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "20") Integer size,
        @RequestParam(defaultValue = "") String username) {

    // 创建分页参数对象
    PageDTO pageDTO = new PageDTO();
    pageDTO.setPageNo(page);
    pageDTO.setPageSize(size);

    // 处理username参数：如果为空字符串，则查询所有用户
    String searchUsername = (username == null || username.trim().isEmpty()) ? null : username.trim();

    // 调用服务层查询用户列表
    Page<UserDTO> result = usersService.getUsersListWithBattleCount(pageDTO, searchUsername);

    return JsonResponse.success(result);
}
```

#### 统计数据服务实现 (`UsersServiceImpl.java`)

```java
/**
 * 获取首页统计数据
 * 功能：统计系统中各类数据的总数，为首页展示提供数据支持
 * @return 包含各种统计信息的Map
 */
@Override
public Map<String, Object> getHomeStats() {
    Map<String, Object> stats = new HashMap<>();

    // 获取总用户数 - 统计users表中的记录总数
    long totalUsers = this.count();
    stats.put("totalUsers", totalUsers);

    // 获取总对战数 - 统计battle_records表中的记录总数
    long totalBattles = battleRecordsService.count();
    stats.put("totalBattles", totalBattles);

    // 获取题目总数 - 统计problems表中的记录总数
    long totalProblems = problemsService.count();
    stats.put("totalProblems", totalProblems);

    // 获取发帖总数 - 统计posts表中的记录总数
    long totalPosts = postsService.count();
    stats.put("totalPosts", totalPosts);

    return stats;
}

/**
 * 获取用户列表（包含对战统计信息）
 * 功能：分页查询用户基本信息，并统计每个用户的对战次数
 * @param pageDTO 分页参数
 * @param username 用户名搜索关键词
 * @return 用户信息分页结果
 */
@Override
public Page<UserDTO> getUsersListWithBattleCount(PageDTO pageDTO, String username) {
    Page<UserDTO> page = new Page<UserDTO>(
        pageDTO.getPageNo(),
        pageDTO.getPageSize()
    );
    // 调用Mapper层执行复杂查询，包含用户信息和对战统计
    page = usersMapper.getUsersListWithBattleCount(page, username);
    return page;
}
```

#### 对战记录接口 (`BattleRecordsController.java`)

```java
/**
 * 获取最近的对战记录
 * 功能：分页查询最近的对战记录，包含题目信息和参与者信息
 */
@GetMapping("/recent")
@ResponseBody
public JsonResponse<Page<BattleRecordWithDetailsDTO>> getRecentBattleRecords(
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "10") Integer size) {
    try {
        // 创建分页参数
        PageDTO pageDTO = new PageDTO();
        pageDTO.setPageNo(page);
        pageDTO.setPageSize(size);

        // 调用服务层获取对战记录详情
        Page<BattleRecordWithDetailsDTO> result = battleRecordsService.getRecentBattleRecords(pageDTO);
        return JsonResponse.success(result);
    } catch (Exception e) {
        return JsonResponse.failure("获取对战记录失败: " + e.getMessage());
    }
}
```

#### 对战记录服务实现 (`BattleRecordsServiceImpl.java`)

```java
/**
 * 获取最近的对战记录（包含详细信息）
 * 功能：查询最近的对战记录，关联查询题目信息和参与者信息
 * @param pageDTO 分页参数
 * @return 对战记录详情分页结果
 */
@Override
public Page<BattleRecordWithDetailsDTO> getRecentBattleRecords(PageDTO pageDTO) {
    Page<BattleRecordWithDetailsDTO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
    // 调用Mapper执行复杂关联查询
    return battleRecordsMapper.getRecentBattleRecords(page);
}
```

### 4. 样式设计实现

#### 主页样式 (`Home.vue` 样式部分)

```scss
<style lang="scss" scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;

  // 欢迎横幅样式
  .welcome-banner {
    margin-bottom: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .banner-content {
      text-align: center;
      padding: 40px 0;

      h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      p {
        font-size: 1.2em;
        margin-bottom: 30px;
        opacity: 0.9;
      }
    }
  }

  // 统计数据行样式
  .stats-row {
    margin-bottom: 20px;

    .stat-card {
      .stat-item {
        display: flex;
        align-items: center;

        .stat-icon {
          font-size: 2em;
          color: #409EFF;
          margin-right: 15px;
        }

        .stat-content {
          .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
          }

          .stat-label {
            color: #666;
            font-size: 0.9em;
          }
        }
      }
    }
  }

  // 主要功能区域样式
  .main-features {
    margin-bottom: 20px;
  }

  // 功能卡片通用样式
  .feature-card {
    margin-bottom: 20px;
    height: 650px; // 增加高度以更好地显示10条数据

    .el-card__body {
      height: calc(100% - 60px); // 减去header高度
      display: flex;
      flex-direction: column;
      padding: 15px;
    }

    // 内容区域样式
    .ranking-preview, .battle-history, .forum-preview {
      flex: 1;
      overflow-y: auto; // 允许滚动
      max-height: 520px; // 增加最大高度

      // 列表项通用样式
      .ranking-item, .battle-item, .post-item {
        display: flex;
        align-items: center;
        padding: 10px 0; // 适当的padding
        border-bottom: 1px solid #f0f0f0;
        min-height: 50px; // 增加最小高度

        &:last-child {
          border-bottom: none;
        }
      }
    }

    // 排行榜特定样式
    .ranking-preview {
      .ranking-item {
        .rank-number {
          width: 30px;
          height: 30px;
          background: #409EFF;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-right: 10px;
          font-size: 14px;
        }

        .user-info {
          flex: 1;
          margin-left: 10px;

          .username {
            font-weight: bold;
            color: #333;
          }

          .rating {
            color: #666;
            font-size: 0.9em;
          }
        }
      }
    }

    // 对战记录特定样式
    .battle-history {
      .battle-item {
        .battle-info {
          flex: 1;

          .battle-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
          }

          .battle-time {
            color: #666;
            font-size: 0.8em;
            margin-bottom: 3px;
          }

          .battle-participants {
            color: #999;
            font-size: 0.8em;
          }
        }

        .battle-result {
          margin-left: 10px;
        }
      }
    }

    // 论坛帖子特定样式
    .forum-preview {
      .post-item {
        .post-content {
          flex: 1;

          .post-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            cursor: pointer;

            &:hover {
              color: #409EFF;
            }
          }

          .post-meta {
            color: #666;
            font-size: 0.8em;

            span {
              margin-right: 10px;
            }
          }
        }
      }
    }

    // 查看更多按钮样式
    .view-more {
      text-align: center;
      padding: 10px 0;
      border-top: 1px solid #f0f0f0;
      margin-top: 10px;
    }
  }
}
</style>
```

#### Dashboard布局样式 (`Dashboard.vue` 样式部分)

```scss
<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  // 主导航栏样式
  .main-header {
    height: 60px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px; // 减少左右内边距
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 1200px; // 设置最小宽度确保有足够空间

    // Logo区域样式
    .logo-section {
      flex-shrink: 0; // 防止logo区域被压缩
      width: 200px; // 固定logo区域宽度

      .logo {
        height: 40px;
        width: auto;
        max-width: 180px; // 限制logo最大宽度
      }
    }

    // 导航菜单区域样式
    .nav-section {
      flex: 1;
      display: flex;
      justify-content: center;
      min-width: 0; // 允许flex项目收缩

      .nav-menu {
        border-bottom: none;
        background-color: transparent;

        .el-menu-item {
          height: 60px;
          line-height: 60px;
          border-bottom: 2px solid transparent;
          margin: 0 10px;
          padding: 0 15px;
          transition: all 0.3s ease;

          &:hover {
            background-color: rgba(64, 158, 255, 0.1);
            border-bottom-color: #409EFF;
          }

          &.is-active {
            color: #409EFF;
            border-bottom-color: #409EFF;
            background-color: rgba(64, 158, 255, 0.1);
          }

          .el-icon {
            margin-right: 5px;
          }
        }
      }
    }

    // 用户区域样式
    .user-section {
      flex-shrink: 0;
      width: 200px;
      display: flex;
      justify-content: flex-end;

      .user-dropdown {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 20px;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: rgba(64, 158, 255, 0.1);
        }

        .user-avatar {
          margin-right: 8px;
        }

        .dropdown-icon {
          color: #666;
          transition: transform 0.3s ease;
        }

        &:hover .dropdown-icon {
          transform: rotate(180deg);
        }
      }
    }
  }

  // 主内容区域样式
  .main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f5f5f5;
  }
}

// 全局样式，确保Element Plus菜单不会自动隐藏项目
.nav-menu.el-menu--horizontal {
  width: 100% !important;
  overflow: visible !important;

  .el-menu-item {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
  }

  // 强制显示所有菜单项
  .el-menu-item:nth-child(1),
  .el-menu-item:nth-child(2),
  .el-menu-item:nth-child(3),
  .el-menu-item:nth-child(4),
  .el-menu-item:nth-child(5),
  .el-menu-item:nth-child(6) {
    display: inline-block !important;
    visibility: visible !important;
  }

  // 隐藏可能的更多按钮
  .el-menu-item.is-disabled,
  .el-sub-menu.is-disabled {
    display: none !important;
  }
}
</style>
```

### 5. Logo组件实现 (`CodeDuelLogo.vue`)

#### SVG Logo设计

```vue
<template>
  <!-- Logo容器，支持自定义宽高 -->
  <div class="logo-container" :style="{ width: width + 'px', height: height + 'px' }">
    <svg
      :width="width"
      :height="height"
      :viewBox="computedViewBox"
      xmlns="http://www.w3.org/2000/svg"
      class="codeduel-logo"
    >
      <!-- SVG渐变和滤镜定义区域 -->
      <defs>
        <!-- 主要渐变色：系统主蓝色，用于括号和交叉点 -->
        <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#409EFF;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#337ecc;stop-opacity:1" />
        </linearGradient>

        <!-- 强调渐变色：用于对战剑和特殊效果 -->
        <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>

        <!-- 发光滤镜：用于悬停效果 -->
        <filter id="glow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      <!-- Logo图标部分：代码括号 + 对战剑 -->
      <g class="logo-icon">
        <!-- 左括号 -->
        <path
          class="bracket-left"
          d="M 25 15 Q 20 15 20 20 L 20 40 Q 20 45 25 45"
          stroke="url(#primaryGradient)"
          stroke-width="2"
          fill="none"
          stroke-linecap="round"
        />

        <!-- 右括号 -->
        <path
          class="bracket-right"
          d="M 45 15 Q 50 15 50 20 L 50 40 Q 50 45 45 45"
          stroke="url(#primaryGradient)"
          stroke-width="2"
          fill="none"
          stroke-linecap="round"
        />

        <!-- 对战剑组合 -->
        <g class="battle-swords">
          <!-- 第一把剑 -->
          <line
            class="sword1"
            x1="30"
            y1="25"
            x2="40"
            y2="35"
            stroke="url(#accentGradient)"
            stroke-width="2"
            stroke-linecap="round"
          />

          <!-- 第二把剑 -->
          <line
            class="sword2"
            x1="40"
            y1="25"
            x2="30"
            y2="35"
            stroke="url(#accentGradient)"
            stroke-width="2"
            stroke-linecap="round"
          />

          <!-- 交叉点发光效果 -->
          <circle
            class="cross-glow"
            cx="35"
            cy="30"
            r="2"
            fill="url(#accentGradient)"
            opacity="0.6"
          />
        </g>
      </g>

      <!-- Logo文字部分 -->
      <g class="logo-text">
        <!-- 主标题：Code Duel -->
        <text
          class="main-text"
          :x="textConfig.codeX"
          :y="textConfig.mainY"
          :font-size="textConfig.mainSize"
          font-family="Arial, sans-serif"
          font-weight="bold"
          fill="url(#primaryGradient)"
        >
          Code
        </text>
        <text
          class="main-text"
          :x="textConfig.duelX"
          :y="textConfig.mainY"
          :font-size="textConfig.mainSize"
          font-family="Arial, sans-serif"
          font-weight="bold"
          fill="url(#accentGradient)"
        >
          Duel
        </text>

        <!-- 副标题：代码对战平台 -->
        <text
          class="subtitle"
          x="70"
          :y="textConfig.subtitleY"
          :font-size="textConfig.subtitleSize"
          font-family="Arial, sans-serif"
          fill="#666"
        >
          代码对战平台
        </text>
      </g>
    </svg>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue'

// 组件属性定义
const props = defineProps({
  width: {
    type: Number,
    default: 180  // 默认宽度 180px
  },
  height: {
    type: Number,
    default: 54   // 默认高度 54px
  },
  showSubtitle: {
    type: Boolean,
    default: false
  }
})

// 动态计算 SVG 的 viewBox
const computedViewBox = computed(() => {
  if (props.showSubtitle) {
    return "0 0 200 60"  // 标准模式：完整显示区域
  } else {
    return "0 0 200 60"  // 导航栏模式：保持完整高度确保副标题可见
  }
})

// 文字配置计算 - 根据显示模式动态调整文字的位置、大小等属性
const textConfig = computed(() => {
  if (props.showSubtitle) {
    // 标准模式：正常显示副标题
    return {
      codeX: 70,        // "Code" 文字的 X 坐标
      duelX: 115,       // "Duel" 文字的 X 坐标
      mainY: 25,        // 主标题的 Y 坐标
      subtitleY: 40,    // 副标题的 Y 坐标
      mainSize: 16,     // 主标题字体大小
      subtitleSize: 9   // 副标题字体大小
    }
  } else {
    // 导航栏模式：紧凑布局
    return {
      codeX: 70,
      duelX: 115,
      mainY: 22,
      subtitleY: 42,
      mainSize: 16,
      subtitleSize: 16
    }
  }
})
</script>

<style lang="scss" scoped>
.logo-container {
  display: inline-block;

  .codeduel-logo {
    // 基础过渡动画：所有变化都有平滑过渡效果
    transition: all 0.3s ease;

    // 悬停效果：鼠标悬停时的交互动画
    &:hover {
      // 代码括号悬停效果：加粗并添加发光
      .bracket-left,
      .bracket-right {
        stroke-width: 3;        // 线条加粗
        filter: url(#glow);     // 添加发光效果
      }

      // 对战剑悬停效果
      .battle-swords {
        .sword1,
        .sword2 {
          stroke-width: 2.5;     // 剑的线条加粗
        }

        // 交叉点发光增强
        .cross-glow {
          r: 4;                  // 发光半径增大
          opacity: 0.8;          // 透明度增加
        }
      }

      // 主标题文字发光效果
      .main-text {
        filter: url(#glow);
      }
    }
  }

  // 响应式设计：移动端适配
  @media (max-width: 768px) {
    .codeduel-logo {
      // 移动端副标题字体调整
      .subtitle {
        font-size: 8px;
      }

      // 移动端主标题字体调整
      .main-text {
        font-size: 14px;
      }
    }
  }
}
</style>
```

## 系统主页子功能详解

### 1. 统计数据展示
- **总用户数**: 实时统计注册用户总量
- **总对战数**: 统计所有已完成的对战记录
- **题目总数**: 显示系统中可用的编程题目数量
- **发帖总数**: 统计论坛中的帖子总数

### 2. 排行榜预览
- **Top 10展示**: 显示Rating最高的前10名用户
- **用户信息**: 包含头像、用户名、Rating分数、对战场次
- **实时更新**: 数据与排行榜页面同步更新

### 3. 最近对战记录
- **对战历史**: 显示最近10场对战的详细信息
- **题目信息**: 展示对战使用的编程题目
- **参与者**: 显示对战的参与用户
- **对战类型**: 区分房间对战和匹配对战

### 4. 论坛动态
- **最新帖子**: 展示最近发布的论坛帖子
- **帖子信息**: 包含标题、作者、发布时间、评论数
- **置顶标识**: 特殊标记置顶帖子

### 5. 快速导航
- **一键对战**: 直接跳转到对战页面开始匹配
- **完整排行榜**: 查看详细的用户排行信息
- **对战历史**: 查看个人详细对战记录
- **论坛入口**: 进入完整的论坛讨论区

## 技术实现亮点

### 1. 响应式设计
- 采用Element Plus的栅格系统实现响应式布局
- 支持不同屏幕尺寸的自适应显示
- 移动端优化的交互体验

### 2. 数据加载优化
- 使用Promise.all并行加载多个数据源
- 统一的错误处理和用户提示
- 加载状态管理，提升用户体验

### 3. 组件化设计
- Logo组件独立封装，支持多种尺寸和显示模式
- 统计卡片组件化，便于复用和维护
- 模块化的样式管理

### 4. 用户体验优化
- 平滑的过渡动画效果
- 悬停交互反馈
- 直观的数据可视化展示

### 5. 性能优化
- 分页加载减少数据传输量
- 缓存机制减少重复请求
- 懒加载和虚拟滚动支持

## 部署配置

### 前端配置 (`vite.config.js`)
```javascript
export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 5174,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8080',
        changeOrigin: true,
        secure: false,
        rewrite: path => path.replace(/^\/api/, '/api')
      }
    }
  }
})
```

### 全局样式配置 (`main.scss`)
```scss
body {
  margin: 0;
  background-color: #f5f5f5;
}

/* 页面过渡动画 */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}

.fade-slide-enter-from {
  transform: translateX(-30px);
  opacity: 0;
}

.fade-slide-leave-to {
  transform: translateX(30px);
  opacity: 0;
}
```

## 总结

CodeDuel系统主页通过精心设计的布局和功能模块，为用户提供了一个信息丰富、操作便捷的入口界面。主要实现了：

1. **数据概览**: 通过统计卡片直观展示系统整体数据
2. **快速导航**: 提供便捷的功能入口和页面跳转
3. **实时信息**: 展示最新的排行榜、对战记录和论坛动态
4. **用户体验**: 响应式设计和流畅的交互动画
5. **性能优化**: 高效的数据加载和渲染机制

整个主页系统采用现代化的前后端分离架构，具有良好的可维护性和扩展性，为用户提供了优秀的代码对战平台体验。
