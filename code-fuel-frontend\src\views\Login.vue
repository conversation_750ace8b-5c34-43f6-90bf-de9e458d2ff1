<script setup>
import {onMounted, ref} from 'vue';
import axios from "axios";
import {usersLogin, generateVerificationCode} from "../api/api";
import {ElMessage} from "element-plus";
import {useRouter} from 'vue-router'
import {Refresh} from '@element-plus/icons-vue'
import CodeDuelLogo from '@/components/CodeDuelLogo.vue'
import VerificationDialog from '@/components/VerificationDialog.vue'
import { useUserInfoStore } from '@/stores/userInfo'

const router = useRouter()
const userInfoStore = useUserInfoStore()
const usersname = ref('')
const password = ref('')
const confirmPassword = ref('')
const captchaInput = ref('')
const captchaUrl = ref('')
const isRegisterMode = ref(false)

// Codeforces验证相关
const showVerificationDialog = ref(false)
const verificationCode = ref('')

// 定义对 DOM 元素的引用
const container = ref(null);
const signUpButton = ref(null);
const signInButton = ref(null);

// 生成验证码
const generateCaptcha = () => {
  const canvas = document.createElement('canvas')
  canvas.width = 120
  canvas.height = 40
  const ctx = canvas.getContext('2d')

  // 设置背景
  ctx.fillStyle = '#f8f9fa'
  ctx.fillRect(0, 0, 120, 40)

  // 生成随机验证码
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let captchaText = ''
  for (let i = 0; i < 4; i++) {
    captchaText += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  // 绘制验证码文字
  ctx.fillStyle = '#333'
  ctx.font = 'bold 18px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  // 添加字符倾斜和颜色变化
  for (let i = 0; i < captchaText.length; i++) {
    ctx.save()
    ctx.fillStyle = `hsl(${Math.random() * 360}, 70%, 40%)`
    ctx.translate(20 + i * 20, 20)
    ctx.rotate((Math.random() - 0.5) * 0.4)
    ctx.fillText(captchaText[i], 0, 0)
    ctx.restore()
  }

  // 添加干扰线
  for (let i = 0; i < 6; i++) {
    ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.3)`
    ctx.lineWidth = 1
    ctx.beginPath()
    ctx.moveTo(Math.random() * 120, Math.random() * 40)
    ctx.lineTo(Math.random() * 120, Math.random() * 40)
    ctx.stroke()
  }

  // 添加干扰点
  for (let i = 0; i < 30; i++) {
    ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.4)`
    ctx.beginPath()
    ctx.arc(Math.random() * 120, Math.random() * 40, 1, 0, 2 * Math.PI)
    ctx.fill()
  }

  captchaUrl.value = canvas.toDataURL()
  window.correctCaptcha = captchaText
}

// 切换模式并清空表单
const switchMode = (mode) => {
  isRegisterMode.value = mode
  usersname.value = ''
  password.value = ''
  confirmPassword.value = ''
  captchaInput.value = ''
  generateCaptcha()
}

onMounted(() => {
  generateCaptcha()

  if (signUpButton.value && signInButton.value && container.value) {
    // 为注册按钮添加点击事件
    signUpButton.value.addEventListener('click', () => {
      container.value.classList.add("right-panel-active");
      switchMode(true)
    });
    // 为登录按钮添加点击事件
    signInButton.value.addEventListener('click', () => {
      container.value.classList.remove("right-panel-active");
      switchMode(false)
    });
  }
});

// 验证验证码
const validateCaptcha = () => {
  // 临时跳过验证码验证，用于测试Codeforces注册功能
  // return true
  return captchaInput.value.toUpperCase() === window.correctCaptcha
}

const login = () => {
  if (!usersname.value || !password.value) {
    ElMessage.error('请输入用户名和密码');
    return;
  }

  if (!validateCaptcha()) {
    ElMessage.error('验证码错误');
    generateCaptcha()
    captchaInput.value = ''
    return;
  }

  const obj = {
    codeforcesId: usersname.value,
    password: password.value
  }

  usersLogin(obj).then(res => {
    console.log(res)
    if (res.data == null) {
      ElMessage({
        message: '用户名密码错误',
        type: 'warning',
      })
      generateCaptcha()
      captchaInput.value = ''
    } else {
      // 检查用户是否被封禁
      if (res.data.status === 1) {
        // 用户被封禁，显示封禁信息
        const banReason = res.data.banReason || '违反平台规定'
        ElMessage({
          message: `账户已被封禁，原因：${banReason}`,
          type: 'error',
          duration: 5000,
          showClose: true
        })
        generateCaptcha()
        captchaInput.value = ''
        return
      }

      // 登录成功，保存用户信息到store（包含更新后的最后登录时间）
      userInfoStore.setUserInfo(res.data)

      ElMessage.success('登录成功')
      router.push('/dashboard/home')
    }
  }).catch(error => {
    console.error('登录错误:', error);
    ElMessage.error('登录失败，请检查网络连接');
    generateCaptcha()
    captchaInput.value = ''
  })
}

const register = async () => {
  if (!usersname.value || !password.value || !confirmPassword.value) {
    ElMessage.error('请填写完整信息');
    return;
  }

  if (password.value !== confirmPassword.value) {
    ElMessage.error('两次密码输入不一致');
    return;
  }

  // 临时取消验证
  // if (!validateCaptcha()) {
  //   ElMessage.error('验证码错误');
  //   generateCaptcha()
  //   captchaInput.value = ''
  //   return;
  // }

  try {
    console.log('🚀 开始注册流程...');
    console.log('用户名:', usersname.value);
    console.log('密码长度:', password.value.length);

    // 生成验证码
    console.log('📝 正在生成验证码...');
    const response = await generateVerificationCode();
    console.log('📥 验证码响应:', response);

    if (response && response.status) {
      verificationCode.value = response.data;
      console.log('✅ 验证码生成成功:', verificationCode.value);
      console.log('🔄 准备显示验证对话框...');
      showVerificationDialog.value = true;
      console.log('📱 对话框状态:', showVerificationDialog.value);

      // 添加延迟检查对话框是否真的显示了
      setTimeout(() => {
        console.log('🔍 延迟检查对话框状态:', showVerificationDialog.value);
        if (!showVerificationDialog.value) {
          console.error('❌ 对话框未能显示，可能是组件问题');
          ElMessage.error('验证对话框加载失败，请刷新页面重试');
        }
      }, 500);

    } else {
      console.error('❌ 验证码生成失败:', response);
      ElMessage.error('生成验证码失败，请重试');
    }
  } catch (error) {
    console.error('💥 注册错误:', error);
    ElMessage.error('注册失败，请重试');
    generateCaptcha()
    captchaInput.value = ''
  }
}

// 处理验证成功
const handleVerificationSuccess = () => {
  ElMessage.success('注册成功，请登录');
  showVerificationDialog.value = false;
  // 切换到登录模式
  container.value.classList.remove("right-panel-active");
  switchMode(false);
  // 清空表单
  usersname.value = '';
  password.value = '';
  confirmPassword.value = '';
  captchaInput.value = '';
  generateCaptcha();
}

// 处理验证取消
const handleVerificationCancel = () => {
  showVerificationDialog.value = false;
}

</script>

<template>
  <div class="login-container">
    <div class="container" id="container" ref="container">
      <div class="form-container sign-up-container">
        <form action="#" @submit.prevent="register">
          <h1>创建账户</h1>
          <p class="form-subtitle">开启你的编程竞技之旅</p>
          <div class="social-container">
            <!--            <a href="#" class="social"><i class="fab fa-facebook-f"></i></a>-->
            <!--            <a href="#" class="social"><i class="fab fa-google-plus-g"></i></a>-->
            <!--            <a href="#" class="social"><i class="fab fa-linkedin-in"></i></a>-->
          </div>
          <!--          <span>or use your email for registration</span>-->
          <el-input v-model="usersname" class="custom-input" placeholder="请输入用户名"/>
          <el-input v-model="password" class="custom-input" placeholder="请输入密码" show-password/>
          <el-input v-model="confirmPassword" class="custom-input" placeholder="请确认密码" show-password/>

          <!-- 验证码区域 -->
          <div class="captcha-container">
            <el-input
                v-model="captchaInput"
                class="captcha-input"
                placeholder="请输入验证码"
                maxlength="4"
            />
            <div class="captcha-image-container">
              <img
                  :src="captchaUrl"
                  alt="验证码"
                  class="captcha-image"
                  @click="generateCaptcha"
                  title="点击刷新验证码"
              />
              <el-button
                  :icon="Refresh"
                  class="refresh-btn"
                  @click="generateCaptcha"
                  title="刷新验证码"
              />
            </div>
          </div>

          <el-button type="primary" @click="register" class="submit-btn">立即注册</el-button>
        </form>
      </div>
      <div class="form-container sign-in-container">
        <form action="#" @submit.prevent="login">
          <h1>登录账户</h1>
          <p class="form-subtitle">继续你的编程挑战</p>
          <div class="social-container">
            <!--            <a href="#" class="social"><i class="fab fa-facebook-f"></i></a>-->
            <!--            <a href="#" class="social"><i class="fab fa-google-plus-g"></i></a>-->
            <!--            <a href="#" class="social"><i class="fab fa-linkedin-in"></i></a>-->
          </div>
          <!--          <span>or use your account</span>-->
          <el-input v-model="usersname" class="custom-input" placeholder="请输入用户名"/>
          <el-input v-model="password" class="custom-input" placeholder="请输入密码" show-password/>

          <!-- 验证码区域 -->
          <div class="captcha-container">
            <el-input
                v-model="captchaInput"
                class="captcha-input"
                placeholder="请输入验证码"
                maxlength="4"
            />
            <div class="captcha-image-container">
              <img
                  :src="captchaUrl"
                  alt="验证码"
                  class="captcha-image"
                  @click="generateCaptcha"
                  title="点击刷新验证码"
              />
              <el-button
                  :icon="Refresh"
                  class="refresh-btn"
                  @click="generateCaptcha"
                  title="刷新验证码"
              />
            </div>
          </div>

          <a href="#" class="forgot-password">忘记密码？</a>
          <el-button type="primary" @click="login" class="submit-btn">
            立即登录
          </el-button>
        </form>
      </div>
      <div class="overlay-container">
        <div class="overlay">
          <div class="overlay-panel overlay-left">
            <!-- 注册界面的Logo -->
            <div class="overlay-logo">
              <CodeDuelLogo :width="240" :height="72" :show-subtitle="true" />
            </div>
            <p>欢迎回到 CodeDuel！使用你的账户登录，继续你的编程竞技之旅，挑战更多精彩题目。</p>
            <button class="ghost" id="signIn" ref="signInButton">返回登录</button>
          </div>
          <div class="overlay-panel overlay-right">
            <!-- 登录界面的Logo -->
            <div class="overlay-logo">
              <CodeDuelLogo :width="240" :height="72" :show-subtitle="true" />
            </div>
            <p>加入 CodeDuel 大家庭！创建账户开始你的编程冒险，与全球开发者一起切磋技艺。</p>
            <button class="ghost" id="signUp" ref="signUpButton">立即注册</button>
          </div>
        </div>
      </div>
    </div>

    <footer>
      <p>
        © 2025 CodeDuel - 让编程更有趣，让竞技更精彩
      </p>
    </footer>

    <!-- Codeforces验证对话框 -->
    <VerificationDialog
        v-model:visible="showVerificationDialog"
        :codeforces-id="usersname"
        :password="password"
        :verification-code="verificationCode"
        @success="handleVerificationSuccess"
        @cancel="handleVerificationCancel"
    />
  </div>
</template>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');

$bg: #EDF2F0;

$neu-1: #ecf0f3;
$neu-2: #d1d9e6;

$white: #f9f9f9;
$gray: #a0a5a8;
$black: #181818;

$purple: #4B70E2;

// 添加输入框样式

.form {
  &__input {
    width: 350px !important; // 使用 !important 提高优先级
    height: 40px;
    margin: 4px 0;
    padding-left: 25px;
    font-size: 13px;
    letter-spacing: 0.15px;
    border: none;
    outline: none;
    font-family: 'Montserrat', sans-serif;
    background-color: $neu-1;
    transition: 0.25s ease;
    border-radius: 8px;
    box-shadow: inset 2px 2px 4px $neu-2,
    inset -2px -2px 4px $white;

    &:focus {
      box-shadow: inset 4px 4px 4px $neu-2,
      inset -4px -4px 4px $white;
    }
  }
}


.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  height: 100vh;
  margin: 0;
  position: relative;
  overflow: hidden;

  // 添加浮动装饰圆圈
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px, 30px 30px;
    animation: sparkle 15s linear infinite;
    pointer-events: none;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes sparkle {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
}

* {
  box-sizing: border-box;
}

h1 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 2.2rem;
  margin: 0 0 12px 0;
  color: #2c3e50;
  letter-spacing: -0.03em;
  line-height: 1.1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-family: 'Inter', sans-serif;
  text-align: center;
  font-weight: 500;
  color: var(--text-regular, #606266);
}

p {
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: 0.005em;
  margin: 20px 0 30px;
  color: var(--text-regular, #606266);
}

.form-subtitle {
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  font-weight: 300;
  color: #7f8c8d;
  margin: 0 0 30px 0;
  line-height: 1.5;
  letter-spacing: 0.01em;
  opacity: 0.9;
}

span {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 400;
}

a {
  color: #333;
  font-size: 14px;
  text-decoration: none;
  margin: 15px 0;
}

button {
  font-family: 'Inter', sans-serif;
  border-radius: var(--border-radius-round, 20px);
  border: 1px solid var(--primary-color, #409EFF);
  background: linear-gradient(135deg, var(--primary-color, #409EFF) 0%, var(--primary-dark, #337ecc) 100%);
  height: 50px;
  color: #FFFFFF;
  font-size: 15px;
  font-weight: 600;
  padding: 15px 50px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(64, 158, 255, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--primary-light, rgba(64, 158, 255, 0.15));
  }

  &.ghost {
    background: transparent;
    border-color: #FFFFFF;
    color: #FFFFFF;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-3px);
      box-shadow: 0 12px 28px rgba(255, 255, 255, 0.25);
    }
  }
}

.submit-btn {
  width: 100%;
  max-width: 350px;
  margin-top: 25px;

  :deep(.el-button) {
    font-family: 'Inter', sans-serif;
    width: 100%;
    height: 52px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.8px;
    text-transform: uppercase;
    background: linear-gradient(135deg, var(--primary-color, #409EFF) 0%, var(--primary-dark, #337ecc) 100%);
    border: none;
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 40px rgba(64, 158, 255, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(-1px);
    }
  }
}

.forgot-password {
  font-family: 'Inter', sans-serif;
  color: #7f8c8d;
  font-size: 14px;
  font-weight: 400;
  text-decoration: none;
  margin: 20px 0;
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: -2px;
    left: 50%;
    background: var(--primary-color, #409EFF);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  &:hover {
    color: var(--primary-color, #409EFF);

    &::after {
      width: 100%;
    }
  }
}

form {
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 45px 55px;
  height: 100%;
  text-align: center;
  font-family: 'Inter', sans-serif;
}

//input {
//  background-color: #eee;
//  border: none;
//  padding: 12px 15px;
//  margin: 8px 0;
//  width: 100%;
//}

.custom-input {
  height: 48px;
  width: 100%;
  max-width: 350px;
  margin: 12px 0;

  :deep(.el-input__wrapper) {
    font-family: 'Inter', sans-serif;
    border-radius: 10px;
    border: 2px solid #e8ecef;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    background: #fafbfc;

    &:hover {
      border-color: var(--primary-color, #409EFF);
      background: #ffffff;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    }

    &.is-focus {
      border-color: var(--primary-color, #409EFF);
      background: #ffffff;
      box-shadow: 0 0 0 3px var(--primary-light, rgba(64, 158, 255, 0.1));
      transform: translateY(-1px);
    }

    .el-input__inner {
      font-family: 'Inter', sans-serif;
      font-weight: 400;
      letter-spacing: 0.3px;

      &::placeholder {
        color: #95a5a6;
        font-weight: 300;
      }
    }
  }
}

// 验证码容器样式
.captcha-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  max-width: 350px;
  margin: 10px 0;

  .captcha-input {
    flex: 1;
    height: 45px;

    :deep(.el-input__wrapper) {
      border-radius: var(--border-radius-base, 8px);
      border: 1px solid var(--border-base, #DCDFE6);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--primary-color, #409EFF);
      }

      &.is-focus {
        border-color: var(--primary-color, #409EFF);
        box-shadow: 0 0 0 2px var(--primary-light, rgba(64, 158, 255, 0.1));
      }
    }
  }

  .captcha-image-container {
    display: flex;
    align-items: center;
    gap: 5px;

    .captcha-image {
      width: 120px;
      height: 40px;
      border: 1px solid var(--border-base, #DCDFE6);
      border-radius: var(--border-radius-base, 8px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--primary-color, #409EFF);
        transform: scale(1.02);
      }
    }

    .refresh-btn {
      width: 40px;
      height: 40px;
      padding: 0;
      border-radius: var(--border-radius-base, 8px);
      border: 1px solid var(--border-base, #DCDFE6);
      background: var(--white, #FFFFFF);
      color: var(--text-regular, #606266);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--primary-color, #409EFF);
        color: var(--primary-color, #409EFF);
        background: var(--primary-light, rgba(64, 158, 255, 0.1));
      }
    }
  }
}


.container {
  background-color: var(--white, #FFFFFF);
  border-radius: var(--border-radius-large, 12px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
  0 8px 16px rgba(0, 0, 0, 0.08),
  0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  width: 900px;
  max-width: 95%;
  min-height: 550px;
  backdrop-filter: blur(10px);
  z-index: 10;
}

.form-container {
  position: absolute;
  top: 0;
  height: 100%;
  transition: all 0.6s ease-in-out;
}

.sign-in-container {
  left: 0;
  width: 50%;
  z-index: 2;
}

.container.right-panel-active .sign-in-container {
  transform: translateX(100%);
}

.sign-up-container {
  left: 0;
  width: 50%;
  opacity: 0;
  z-index: 1;
}

.container.right-panel-active .sign-up-container {
  transform: translateX(100%);
  opacity: 1;
  z-index: 5;
  animation: show 0.6s;
}

@keyframes show {
  0%, 49.99% {
    opacity: 0;
    z-index: 1;
  }

  50%, 100% {
    opacity: 1;
    z-index: 5;
  }
}

.overlay-container {
  position: absolute;
  top: 0;
  left: 50%;
  width: 50%;
  height: 100%;
  overflow: hidden;
  transition: transform 0.6s ease-in-out;
  z-index: 100;
}

.container.right-panel-active .overlay-container {
  transform: translateX(-100%);
}

.overlay {
  background: linear-gradient(135deg, var(--primary-color, #409EFF) 0%, var(--primary-dark, #337ecc) 100%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 0 0;
  color: #FFFFFF;
  position: relative;
  left: -100%;
  height: 100%;
  width: 200%;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;

  // 添加装饰性渐变覆盖
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
}

.container.right-panel-active .overlay {
  transform: translateX(50%);
}

.overlay-panel {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 45px;
  text-align: center;
  top: 0;
  height: 100%;
  width: 50%;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;

  // Overlay 中的 Logo 样式 - 增强可见性
  .overlay-logo {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 1s ease-out;

    // 增强阴影效果
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);

    // Logo 悬停效果
    &:hover {
      transform: scale(1.08) translateY(-2px);
      background: rgba(255, 255, 255, 0.2);
      box-shadow:
          0 12px 40px rgba(0, 0, 0, 0.15),
          0 6px 20px rgba(255, 255, 255, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    // 脉冲动画效果
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg,
          rgba(255, 255, 255, 0.3),
          rgba(255, 255, 255, 0.1),
          rgba(255, 255, 255, 0.3));
      border-radius: 18px;
      z-index: -1;
      animation: pulse 3s ease-in-out infinite;
    }
  }

  h1 {
    font-family: 'Poppins', sans-serif;
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 18px;
    letter-spacing: -0.02em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
  }

  p {
    font-family: 'Inter', sans-serif;
    color: rgba(255, 255, 255, 0.95);
    font-size: 16px;
    font-weight: 300;
    line-height: 1.7;
    margin-bottom: 35px;
    letter-spacing: 0.3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

.overlay-left {
  transform: translateX(-20%);
}

.container.right-panel-active .overlay-left {
  transform: translateX(0);
}

.overlay-right {
  right: 0;
  transform: translateX(0);
}

.container.right-panel-active .overlay-right {
  transform: translateX(20%);
}

.social-container {
  margin: 20px 0;
}

.social-container a {
  border: 1px solid #DDDDDD;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin: 0 5px;
  height: 40px;
  width: 40px;
}

footer {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  bottom: 0;
  position: fixed;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 999;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  p {
    margin: 10px 0;
    font-weight: 300;
  }

  a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: #FFFFFF;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    width: 95%;
    min-height: 500px;
    margin: 20px 0;
  }

  .custom-input,
  .captcha-container {
    max-width: 280px;
  }

  form {
    padding: 30px 30px;
  }

  .captcha-container {
    flex-direction: column;
    gap: 10px;

    .captcha-image-container {
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .container {
    width: 98%;
    min-height: 450px;
  }

  .custom-input,
  .captcha-container {
    max-width: 250px;
  }

  form {
    padding: 20px 20px;
  }

  h1 {
    font-size: 1.5rem;
  }

  // Overlay Logo 在移动端的调整
  .overlay-panel {
    .overlay-logo {
      margin-bottom: 20px;
      padding: 15px;

      // 移动端 logo 尺寸调整
      :deep(.codeduel-logo) {
        width: 200px !important;
        height: 60px !important;
      }

      // 移动端减少一些效果以保持性能
      &::before {
        display: none;
      }
    }
  }
}

// Logo 淡入上升动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Logo 脉冲动画
@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}
</style>
