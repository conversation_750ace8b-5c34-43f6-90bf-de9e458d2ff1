<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  User,
  Clock,
  ChatDotRound,
  Delete
} from '@element-plus/icons-vue'
import { getPostById, getCommentsByPostId, createComment, deleteComment } from '@/api/api'
import { useUserInfoStore } from '@/stores/userInfo'

const route = useRoute()
const router = useRouter()
const userStore = useUserInfoStore()

// 响应式数据
const loading = ref(false)
const post = ref(null)
const postId = computed(() => route.params.id)

// 评论相关数据
const comments = ref([])
const commentsLoading = ref(false)
const newComment = ref('')
const replyingTo = ref(null)
const replyContent = ref('')

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalComments = ref(0)
const totalPages = ref(1)

// 简单的 Markdown 渲染函数（与Forum.vue中的相同）
const renderMarkdown = (text) => {
  if (!text) return ''

  let html = text
    // 代码块（需要在其他替换之前处理）
    .replace(/```([^`]+)```/gim, '<pre><code>$1</code></pre>')
    // 标题
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // 粗体和斜体
    .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/gim, '<em>$1</em>')
    // 行内代码
    .replace(/`([^`]+)`/gim, '<code>$1</code>')
    // 链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank">$1</a>')

  // 处理列表
  const lines = html.split('\n')
  const processedLines = []
  let inList = false

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    if (line.startsWith('- ')) {
      if (!inList) {
        processedLines.push('<ul>')
        inList = true
      }
      processedLines.push(`<li>${line.substring(2)}</li>`)
    } else if (line.match(/^\d+\. /)) {
      if (!inList) {
        processedLines.push('<ol>')
        inList = true
      }
      processedLines.push(`<li>${line.replace(/^\d+\. /, '')}</li>`)
    } else {
      if (inList) {
        processedLines.push('</ul>')
        inList = false
      }
      if (line) {
        processedLines.push(line)
      } else {
        processedLines.push('<br>')
      }
    }
  }

  if (inList) {
    processedLines.push('</ul>')
  }

  return processedLines.join('')
}

// 渲染后的内容
const renderedContent = computed(() => {
  return post.value ? renderMarkdown(post.value.content) : ''
})



// 获取帖子详情
const loadPostDetail = async () => {
  try {
    loading.value = true

    const response = await getPostById(postId.value)
    console.log('帖子详情API响应:', response)

    if (response.status) {
      const data = response.data

      // 处理帖子数据，转换为前端需要的格式
      // 现在API返回PostWithUserDTO，包含帖子和用户信息
      post.value = {
        id: data.post.id,
        title: data.post.title,
        content: data.post.content,
        isTop: data.post.isTop,
        publishTime: new Date(data.post.postTime),
        commentCount: data.post.likeCount, // likeCount字段实际存储的是评论数
        userId: data.post.userId,
        author: {
          id: data.user.id,
          username: data.user.codeforcesId, // 真实的用户名
          avatar: data.user.avatar || 'https://userpic.codeforces.org/no-title.jpg', // 真实的头像
          rating: data.user.rating || 0
        }
      }

      console.log('处理后的帖子数据:', post.value)
    } else {
      ElMessage.error(response.message || '获取帖子详情失败')
      router.push('/dashboard/forum')
    }
  } catch (error) {
    console.error('获取帖子详情失败:', error)
    ElMessage.error('获取帖子详情失败')
    router.push('/dashboard/forum')
  } finally {
    loading.value = false
  }
}

// 返回论坛列表
const goBack = () => {
  router.push('/dashboard/forum')
}

// 查看用户资料
const viewUserProfile = (username) => {
  router.push(`/dashboard/profile/${username}`)
}

// 加载评论列表（分页）
const loadComments = async () => {
  try {
    commentsLoading.value = true

    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    const response = await getCommentsByPostId(postId.value, params)

    if (response.status) {
      const data = response.data
      comments.value = data.records || []
      totalComments.value = data.total || 0
      totalPages.value = data.pages || 1
      currentPage.value = data.current || 1
    } else {
      ElMessage.error(response.message || '获取评论失败')
    }
  } catch (error) {
    console.error('获取评论失败:', error)
    ElMessage.error('获取评论失败')
  } finally {
    commentsLoading.value = false
  }
}

// 发布评论
const submitComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  if (!userStore.userInfo.id) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    const commentData = {
      postId: parseInt(postId.value),
      userId: userStore.userInfo.id,
      content: newComment.value.trim(),
      parentId: null
    }

    const response = await createComment(commentData)

    if (response.status) {
      ElMessage.success('评论发布成功')
      newComment.value = ''
      // 发布新评论后跳到第一页显示最新评论
      currentPage.value = 1
      // 更新评论总数
      totalComments.value += 1
      await loadComments()
    } else {
      ElMessage.error(response.message || '评论发布失败')
    }
  } catch (error) {
    console.error('发布评论失败:', error)
    ElMessage.error('发布评论失败')
  }
}

// 回复评论
const submitReply = async (parentId) => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }

  if (!userStore.userInfo.id) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    const replyData = {
      postId: parseInt(postId.value),
      userId: userStore.userInfo.id,
      content: replyContent.value.trim(),
      parentId: parentId
    }

    const response = await createComment(replyData)

    if (response.status) {
      ElMessage.success('回复发布成功')
      replyContent.value = ''
      replyingTo.value = null
      // 发布回复后保持当前页面
      await loadComments()
    } else {
      ElMessage.error(response.message || '回复发布失败')
    }
  } catch (error) {
    console.error('发布回复失败:', error)
    ElMessage.error('发布回复失败')
  }
}

// 显示回复框
const showReplyBox = (commentId) => {
  replyingTo.value = commentId
  replyContent.value = ''
}

// 取消回复
const cancelReply = () => {
  replyingTo.value = null
  replyContent.value = ''
}

// 分页处理方法
const handleSizeChange = async (val) => {
  pageSize.value = val
  currentPage.value = 1 // 切换每页数量时重置页码为 1
  await loadComments()
}

const handleCurrentChange = async (val) => {
  currentPage.value = val
  await loadComments()
}

// 删除评论
const handleDeleteComment = async (commentId, commentUserId) => {
  try {
    // 确认删除
    await ElMessageBox.confirm(
      '确定要删除这条评论吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const params = {
      userId: userStore.userInfo.id,
      isAdmin: userStore.userInfo.isAdmin === 1
    }

    const response = await deleteComment(commentId, params)

    if (response.status) {
      ElMessage.success('评论删除成功')
      // 更新评论总数
      if (totalComments.value > 0) {
        totalComments.value -= 1
      }
      await loadComments() // 重新加载评论列表
    } else {
      ElMessage.error(response.message || '删除评论失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消删除
      return
    }
    console.error('删除评论失败:', error)
    ElMessage.error('删除评论失败')
  }
}

// 判断是否可以删除评论
const canDeleteComment = (commentUserId) => {
  if (!userStore.userInfo.id) return false

  // 管理员可以删除任意评论
  if (userStore.userInfo.isAdmin === 1) return true

  // 普通用户只能删除自己的评论
  return userStore.userInfo.id === commentUserId
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''

  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date

  // 小于1分钟
  if (diff < 60000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  }

  // 小于24小时
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  }

  // 小于7天
  if (diff < 604800000) {
    return `${Math.floor(diff / 86400000)}天前`
  }

  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  if (postId.value) {
    loadPostDetail()
    loadComments()
  } else {
    ElMessage.error('帖子ID无效')
    router.push('/dashboard/forum')
  }
})
</script>

<template>
  <div class="post-detail-container" v-loading="loading">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button @click="goBack" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回讨论区
      </el-button>
    </div>

    <!-- 帖子内容 -->
    <el-card class="post-card" v-if="post">
      <!-- 帖子头部 -->
      <div class="post-header">
        <div class="post-title-section">
          <h1 class="post-title">
            {{ post.title }}
            <span v-if="post.isTop === 1" class="top-badge">置顶</span>
          </h1>
        </div>
        
        <!-- 作者信息和发布时间 -->
        <div class="post-meta">
          <div class="author-info" @click="viewUserProfile(post.author.username)">
            <img :src="post.author.avatar" :alt="post.author.username + '的头像'" class="author-avatar">
            <div class="author-details">
              <span class="author-name">{{ post.author.username }}</span>
              <span class="author-rating" v-if="post.author.rating > 0">Rating: {{ post.author.rating }}</span>
            </div>
          </div>
          
          <div class="post-time-info">
            <div class="time-item">
              <el-icon><Clock /></el-icon>
              <span>{{ formatTime(post.publishTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 帖子内容 -->
      <div class="post-content">
        <div class="content-body" v-html="renderedContent"></div>
      </div>

      <!-- 帖子底部统计 -->
      <!-- <div class="post-footer">
        <div class="post-stats">
          <div class="stat-item">
            <el-icon><ChatDotRound /></el-icon>
            <span>{{ post.commentCount }} 回复</span>
          </div>
        </div>
      </div> -->
    </el-card>

    <!-- 评论区 -->
    <el-card class="comments-section" v-if="post">
      <template #header>
        <div class="comments-header">
          <h3>评论区 ({{ totalComments }})</h3>
        </div>
      </template>

      <!-- 发布评论 -->
      <div class="comment-input-section">
        <div class="comment-input-row">
          <img :src="userStore.userInfo.avatar || 'https://userpic.codeforces.org/no-title.jpg'"
               class="user-avatar" alt="我的头像">
          <div class="input-area">
            <el-input
              v-model="newComment"
              type="textarea"
              placeholder="写下你的评论..."
              :rows="3"
              maxlength="1000"
              show-word-limit
            />
            <div class="input-actions">
              <el-button type="primary" @click="submitComment" :disabled="!newComment.trim()">
                发布评论
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 评论列表 -->
      <div class="comments-list" v-loading="commentsLoading">
        <div v-if="comments.length === 0 && !commentsLoading" class="no-comments">
          <el-empty description="暂无评论，快来发表第一条评论吧！" />
        </div>

        <div v-for="comment in comments" :key="comment.commentId" class="comment-item">
          <div class="comment-content">
            <img :src="comment.userAvatar || 'https://userpic.codeforces.org/no-title.jpg'"
                 class="comment-avatar" :alt="comment.username + '的头像'">
            <div class="comment-body">
              <div class="comment-header">
                <span class="comment-author">{{ comment.username }}</span>
              </div>
              <div class="comment-text">
                <span v-if="comment.parentUsername" class="reply-prefix">
                  回复 <span class="mention">@{{ comment.parentUsername }}</span> :
                </span>
                {{ comment.content }}
              </div>
              <div class="comment-footer">
                <span class="comment-time">{{ formatTime(comment.createTime) }}</span>
                <span class="reply-btn" @click="showReplyBox(comment.commentId)">回复</span>
                <span
                  v-if="canDeleteComment(comment.userId)"
                  class="delete-btn"
                  @click="handleDeleteComment(comment.commentId, comment.userId)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </span>
              </div>

              <!-- 回复框 -->
              <div v-if="replyingTo === comment.commentId" class="reply-input-section">
                <div class="comment-input-row">
                  <img :src="userStore.userInfo.avatar || 'https://userpic.codeforces.org/no-title.jpg'"
                       class="user-avatar" alt="我的头像">
                  <div class="input-area">
                    <el-input
                      v-model="replyContent"
                      type="textarea"
                      :placeholder="`回复 @${comment.username}...`"
                      :rows="2"
                      maxlength="1000"
                      show-word-limit
                    />
                    <div class="input-actions">
                      <el-button size="small" @click="cancelReply">取消</el-button>
                      <el-button type="primary" size="small" @click="submitReply(comment.commentId)"
                                 :disabled="!replyContent.trim()">
                        发布回复
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页器 -->
      <div class="pagination-wrapper" v-if="totalComments > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="totalComments"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-empty description="正在加载帖子内容..." />
    </div>

    <!-- 帖子不存在 -->
    <div v-if="!post && !loading" class="not-found-container">
      <el-empty description="帖子不存在或已被删除">
        <el-button type="primary" @click="goBack">返回讨论区</el-button>
      </el-empty>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.post-detail-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;

  .back-button {
    margin-bottom: 20px;
  }

  .post-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;

    .post-header {
      padding-bottom: 20px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 30px;

      .post-title-section {
        margin-bottom: 20px;

        .post-title {
          font-size: 28px;
          font-weight: 700;
          color: #303133;
          margin: 0;
          line-height: 1.3;
          display: flex;
          align-items: center;
          gap: 12px;

          .top-badge {
            display: inline-block;
            padding: 4px 12px;
            font-size: 12px;
            color: #ff6b35;
            background: linear-gradient(135deg, #fff4e6 0%, #ffe7ba 100%);
            border: 1px solid #ff6b35;
            border-radius: 20px;
            font-weight: 600;
          }
        }
      }

      .post-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .author-info {
          display: flex;
          align-items: center;
          cursor: pointer;
          transition: all 0.2s ease;
          padding: 8px;
          border-radius: 8px;

          &:hover {
            background-color: #f5f7fa;
          }

          .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e4e7ed;
            margin-right: 12px;
          }

          .author-details {
            display: flex;
            flex-direction: column;

            .author-name {
              font-size: 16px;
              font-weight: 600;
              color: #409eff;
              margin-bottom: 2px;
            }

            .author-rating {
              font-size: 12px;
              color: #909399;
            }
          }
        }

        .post-time-info {
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          .time-item {
            display: flex;
            align-items: center;
            color: #909399;
            font-size: 14px;
            margin-bottom: 4px;

            .el-icon {
              margin-right: 6px;
            }
          }
        }
      }
    }

    .post-content {
      .content-body {
        line-height: 1.8;
        color: #303133;
        font-size: 16px;
        word-wrap: break-word;

        :deep(h1) {
          font-size: 24px;
          margin: 30px 0 20px 0;
          color: #303133;
          border-bottom: 2px solid #409eff;
          padding-bottom: 10px;
        }

        :deep(h2) {
          font-size: 20px;
          margin: 25px 0 15px 0;
          color: #409eff;
        }

        :deep(h3) {
          font-size: 18px;
          margin: 20px 0 12px 0;
          color: #606266;
        }

        :deep(p) {
          margin: 15px 0;
        }

        :deep(strong) {
          color: #e6a23c;
          font-weight: 600;
        }

        :deep(em) {
          color: #909399;
          font-style: italic;
        }

        :deep(code) {
          background: #f5f7fa;
          padding: 3px 8px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          color: #e6a23c;
          font-size: 14px;
        }

        :deep(pre) {
          background: #f5f7fa;
          padding: 20px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 20px 0;
          border-left: 4px solid #409eff;

          code {
            background: none;
            padding: 0;
            color: #303133;
            font-size: 14px;
          }
        }

        :deep(ul), :deep(ol) {
          margin: 15px 0;
          padding-left: 30px;

          li {
            margin: 8px 0;
          }
        }

        :deep(a) {
          color: #409eff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        :deep(br) {
          line-height: 1.5;
        }
      }
    }

    .post-footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      .post-stats {
        display: flex;
        gap: 30px;

        .stat-item {
          display: flex;
          align-items: center;
          color: #909399;
          font-size: 14px;

          .el-icon {
            margin-right: 6px;
            font-size: 16px;
          }
        }
      }
    }
  }

  // 评论区样式
  .comments-section {
    margin-top: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .comments-header h3 {
      margin: 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }

    .comment-input-section, .reply-input-section {
      margin-bottom: 20px;
    }

    .comment-input-row {
      display: flex;
      gap: 12px;
      align-items: flex-start;
    }

    .user-avatar, .comment-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      flex-shrink: 0;
      border: 2px solid #e4e7ed;
    }

    .input-area {
      flex: 1;
    }

    .input-actions {
      margin-top: 8px;
      text-align: right;
    }

    .comments-list {
      min-height: 100px;
    }

    .no-comments {
      text-align: center;
      padding: 40px 0;
    }

    .comment-item {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }
    }

    .comment-content {
      display: flex;
      gap: 12px;
      align-items: flex-start;
    }

    .comment-body {
      flex: 1;
    }

    .comment-header {
      margin-bottom: 6px;
    }

    .comment-author {
      font-weight: 600;
      color: #303133;
      font-size: 14px;
    }

    .comment-text {
      color: #303133;
      line-height: 1.5;
      margin-bottom: 8px;
      font-size: 14px;

      .reply-prefix {
        color: #666;
      }

      .mention {
        color: #008AC5;
        font-weight: 500;
      }
    }

    .comment-footer {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }

    .comment-time {
      color: #909399;
      font-size: 12px;
    }

    .reply-btn, .delete-btn {
      color: #666;
      font-size: 12px;
      cursor: pointer;
      user-select: none;
      transition: color 0.2s ease;
      display: flex;
      align-items: center;
      gap: 2px;

      &:hover {
        color: #409EFF;
      }
    }

    .delete-btn {
      &:hover {
        color: #F56C6C;
      }
    }

    .reply-input-section {
      margin-top: 12px;
      padding-left: 0;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .loading-container,
  .not-found-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 10px;

    .post-card {
      .post-header {
        .post-title-section {
          .post-title {
            font-size: 22px;
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }

        .post-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: 15px;

          .post-time-info {
            align-items: flex-start;
          }
        }
      }

      .post-content {
        .content-body {
          font-size: 15px;
        }
      }

      .post-footer {
        .post-stats {
          flex-wrap: wrap;
          gap: 20px;
        }
      }
    }

    .comments-section {
      .comment-input-row {
        gap: 8px;
      }

      .user-avatar, .comment-avatar {
        width: 32px;
        height: 32px;
      }

      .comment-text {
        font-size: 13px;
      }

      .input-actions {
        .el-button {
          font-size: 12px;
          padding: 6px 12px;
        }
      }
    }
  }
}
</style>
