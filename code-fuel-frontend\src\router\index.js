import { createRouter, createWebHistory } from "vue-router";
import Login from "@/views/Login.vue";
import Admin from "@/views/Admin.vue";
import UserInfo from "@/views/admin/UserInfo.vue";
import UserList from "@/views/admin/UserList.vue";
import RestPassword from "@/views/user/RestPassword.vue";
import Dashboard from "@/views/Dashboard.vue";
import Home from "@/views/dashboard/Home.vue";
import Forum from "@/views/dashboard/Forum.vue";
import Battle from "@/views/dashboard/Battle.vue";
import Ranking from "@/views/dashboard/Ranking.vue";
import Profile from "@/views/dashboard/Profile.vue";
import Chat from "@/views/dashboard/Chat.vue";
import PostDetail from "@/views/dashboard/PostDetail.vue";
import test from "@/views/user/Test.vue";

// 定义路由关系
const routes = [
  // 根路径重定向到dashboard/home
  { path: '/', redirect: '/dashboard/home' },

  // 登录页面
  { path: '/login', component: Login },

  // Dashboard相关路由
  {
    path: '/dashboard',
    component: Dashboard,
    children: [
      { path: 'home', component: Home },
      { path: 'forum', component: Forum },
      { path: 'forum/post/:id', component: PostDetail },
      { path: 'battle', component: Battle },
      { path: 'ranking', component: Ranking },
      { path: 'chat', component: Chat },
      { path: 'profile/:username', component: Profile },
      { path: '/user/info', component: UserInfo }
    ]
  },

  // Admin相关路由
  {
    path: '/admin',
    component: Admin,
    children: [
      { path: 'userlist', component: UserList },
      { path: 'userinfo', component: UserInfo }
    ]
  },

  // User相关路由
  { path: '/user/resetPassword', component: RestPassword },


  // 测试路由
  { path: '/test', component: test }

]


// 创建路由器
const router = createRouter({
  history: createWebHistory(), // 路由模式
  routes: routes
})

//导出暴露
export default router
