/**
 * 简单的Markdown渲染器
 * 支持常用的Markdown语法
 */

export function renderMarkdown(content) {
  if (!content) return ''

  let html = content

  // 转义HTML特殊字符（除了我们要处理的markdown语法）
  html = html.replace(/&/g, '&amp;')
           .replace(/</g, '&lt;')
           .replace(/>/g, '&gt;')

  // 代码块（必须在其他处理之前）
  html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
    const language = lang || 'text'
    return `<pre class="code-block" data-lang="${language}"><code>${code.trim()}</code></pre>`
  })

  // 行内代码
  html = html.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')

  // 标题
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>')
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>')
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>')

  // 粗体和斜体
  html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>')
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')

  // 删除线
  html = html.replace(/~~(.*?)~~/g, '<del>$1</del>')

  // 链接
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

  // 图片
  html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="markdown-image" />')

  // 无序列表
  html = html.replace(/^\s*[-*+]\s+(.+)$/gm, '<li>$1</li>')
  html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')

  // 有序列表
  html = html.replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>')
  // 注意：这里简化处理，实际应该区分有序和无序列表

  // 引用块
  html = html.replace(/^>\s*(.+)$/gm, '<blockquote>$1</blockquote>')

  // 水平分割线
  html = html.replace(/^---+$/gm, '<hr>')
  html = html.replace(/^\*\*\*+$/gm, '<hr>')

  // 表格（简单处理）
  html = html.replace(/\|(.+)\|/g, (match, content) => {
    const cells = content.split('|').map(cell => cell.trim())
    const cellsHtml = cells.map(cell => `<td>${cell}</td>`).join('')
    return `<tr>${cellsHtml}</tr>`
  })
  html = html.replace(/(<tr>.*<\/tr>)/s, '<table class="markdown-table">$1</table>')

  // 段落处理
  html = html.replace(/\n\n/g, '</p><p>')
  html = '<p>' + html + '</p>'

  // 清理空段落
  html = html.replace(/<p><\/p>/g, '')
  html = html.replace(/<p>(<h[1-6]>)/g, '$1')
  html = html.replace(/(<\/h[1-6]>)<\/p>/g, '$1')
  html = html.replace(/<p>(<ul>)/g, '$1')
  html = html.replace(/(<\/ul>)<\/p>/g, '$1')
  html = html.replace(/<p>(<ol>)/g, '$1')
  html = html.replace(/(<\/ol>)<\/p>/g, '$1')
  html = html.replace(/<p>(<blockquote>)/g, '$1')
  html = html.replace(/(<\/blockquote>)<\/p>/g, '$1')
  html = html.replace(/<p>(<pre)/g, '$1')
  html = html.replace(/(<\/pre>)<\/p>/g, '$1')
  html = html.replace(/<p>(<hr>)<\/p>/g, '$1')
  html = html.replace(/<p>(<table)/g, '$1')
  html = html.replace(/(<\/table>)<\/p>/g, '$1')

  // 换行处理
  html = html.replace(/\n/g, '<br>')

  return html
}

/**
 * 代码高亮（简单版本）
 */
export function highlightCode(code, language) {
  // 这里可以集成更复杂的代码高亮库
  // 目前只做简单的关键字高亮

  const keywords = {
    cpp: ['int', 'void', 'return', 'if', 'else', 'for', 'while', 'class', 'public', 'private', 'protected', 'vector', 'string'],
    javascript: ['function', 'var', 'let', 'const', 'return', 'if', 'else', 'for', 'while', 'class'],
    python: ['def', 'return', 'if', 'else', 'elif', 'for', 'while', 'class', 'import', 'from'],
    java: ['public', 'private', 'protected', 'class', 'interface', 'int', 'void', 'return', 'if', 'else', 'for', 'while']
  }

  if (keywords[language]) {
    keywords[language].forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g')
      code = code.replace(regex, `<span class="keyword">${keyword}</span>`)
    })
  }

  // 字符串高亮
  code = code.replace(/"([^"]*)"/g, '<span class="string">"$1"</span>')
  code = code.replace(/'([^']*)'/g, '<span class="string">\'$1\'</span>')

  // 注释高亮
  code = code.replace(/\/\/(.*)$/gm, '<span class="comment">//$1</span>')
  code = code.replace(/\/\*([\s\S]*?)\*\//g, '<span class="comment">/*$1*/</span>')

  // 数字高亮
  code = code.replace(/\b(\d+)\b/g, '<span class="number">$1</span>')

  return code
}

/**
 * 获取文本摘要
 */
export function getTextExcerpt(content, maxLength = 150) {
  if (!content) return ''

  // 移除markdown语法
  let text = content
    .replace(/[#*`_~\[\]()]/g, '')
    .replace(/!\[.*?\]\(.*?\)/g, '')
    .replace(/\[.*?\]\(.*?\)/g, '')
    .replace(/```[\s\S]*?```/g, '')
    .replace(/\n+/g, ' ')
    .trim()

  if (text.length <= maxLength) {
    return text
  }

  return text.substring(0, maxLength) + '...'
}

/**
 * 计算阅读时间（分钟）
 */
export function calculateReadingTime(content) {
  if (!content) return 0

  const wordsPerMinute = 200 // 平均阅读速度
  const words = content.split(/\s+/).length
  const minutes = Math.ceil(words / wordsPerMinute)

  return Math.max(1, minutes)
}
