<script setup>

import {
  CaretBottom,
  EditPen,
  Promotion,
  SwitchButton,
  User,
  UserFilled,
  HomeFilled
} from '@element-plus/icons-vue'
import avatar from '@/assets/default.png'
import CodeDuelLogo from '@/components/CodeDuelLogo.vue'
import {useUserInfoStore} from "@/stores/userInfo";
import {useRouter} from "vue-router";
import {ElMessage, ElMessageBox} from "element-plus";
import {getInfo} from "@/api/api";

const userInfoStore = useUserInfoStore();
const getUserInfo = ()=>{
  getInfo().then(
      res => {
        if(res.data == null)
        {
          ElMessage.error("暂未登录，请先登录")
          router.push('/login')
        }else{
          userInfoStore.setUserInfo(res.data)
        }
      }
  )
}
getUserInfo()

const router = useRouter();

// 回到首页
const goToHome = () => {
  ElMessageBox.confirm(
      '确定要离开管理后台，回到首页吗？',
      '确认跳转',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
  ).then(() => {
    router.push('/dashboard/home')
    ElMessage.success('已跳转到首页')
  }).catch(() => {
    // 用户取消操作
  })
}

const handleCommand = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm(
        '你确认要退出吗？',
        '温馨提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }
    ).then(
        async () => {
          // clear data in pinia
          userInfoStore.removeUserInfo()
          ElMessage.success("退出成功")
          await router.push('/login')
        }
    )
  } else if (command === 'home') {
    goToHome()
  } else {
    router.push('/user/' + command)
  }
}
</script>

<template>
  <el-container class="layout-container">
    <!-- 左侧菜单 -->
    <el-aside width="200px">
      <div class="el-aside__logo">
        <CodeDuelLogo :width="160" :height="48" :show-subtitle="true" />
      </div>
      <el-menu active-text-color="#ffd04b" background-color="#232323" text-color="#fff"
               router>
        <!-- 回到首页 -->
        <el-menu-item @click="goToHome" style="border-bottom: 1px solid #404040; margin-bottom: 10px;">
          <el-icon>
            <HomeFilled/>
          </el-icon>
          <span>回到首页</span>
        </el-menu-item>

        <el-menu-item index="/admin/userlist">
          <el-icon>
            <Promotion/>
          </el-icon>
          <span>用户管理</span>
        </el-menu-item>
        <!-- <el-sub-menu>
          <template #title>
            <el-icon>
              <UserFilled/>
            </el-icon>
            <span>个人中心</span>
          </template>
          <el-menu-item index="/admin/userinfo">
            <el-icon>
              <User/>
            </el-icon>
            <span>基本资料</span>
          </el-menu-item>
          <el-menu-item index="/user/resetPassword">
            <el-icon>
              <EditPen/>
            </el-icon>
            <span>重置密码</span>
          </el-menu-item>
        </el-sub-menu> -->
      </el-menu>
    </el-aside>
    <!-- 右侧主区域 -->
    <el-container>
      <!-- 头部区域 -->
      <el-header>
        <div>欢迎：<strong>{{ userInfoStore.userInfo.codeforcesId }}</strong></div>
        <el-dropdown placement="bottom-end" @command="handleCommand">
                    <span class="el-dropdown__box">
                        <el-avatar :src="userInfoStore.userInfo.avatar?userInfoStore.userInfo.avatar:avatar"/>
                        <el-icon>
                            <CaretBottom/>
                        </el-icon>
                    </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="home" :icon="HomeFilled">回到首页</el-dropdown-item>
              <el-dropdown-item divided command="info" :icon="User">基本资料</el-dropdown-item>
              <!-- <el-dropdown-item command="resetPassword" :icon="EditPen">重置密码</el-dropdown-item> -->
              <el-dropdown-item divided command="logout" :icon="SwitchButton">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-header>
      <!-- 中间区域 -->
      <el-main>
        <router-view/>
      </el-main>
      <!-- 底部区域 -->
      <el-footer>后台管理系统 ©2025 </el-footer>
    </el-container>
  </el-container>
</template>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;

  .el-aside {
    background-color: #232323;

    &__logo {
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
    }

    .el-menu {
      border-right: none;
    }
  }

  .el-header {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-dropdown__box {
      display: flex;
      align-items: center;

      .el-icon {
        color: #999;
        margin-left: 10px;
      }

      &:active,
      &:focus {
        outline: none;
      }
    }
  }

  .el-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
  }
}
</style>
