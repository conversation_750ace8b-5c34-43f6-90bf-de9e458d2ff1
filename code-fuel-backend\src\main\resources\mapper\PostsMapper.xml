<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xju.codeduel.mapper.PostsMapper">

    <!-- 基础结果映射：Posts实体 -->
    <resultMap id="BasePostsResultMap" type="com.xju.codeduel.model.domain.Posts">
        <id column="p_id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="is_top" property="isTop"/>
        <result column="post_time" property="postTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="like_count" property="likeCount"/>
    </resultMap>

    <!-- 基础结果映射：Users实体 -->
    <resultMap id="BaseUsersResultMap" type="com.xju.codeduel.model.domain.Users">
        <id column="u_id" property="id"/>
        <result column="password" property="password"/>
        <result column="codeforces_id" property="codeforcesId"/>
        <result column="avatar" property="avatar"/>
        <result column="rating" property="rating"/>
        <result column="status" property="status"/>
        <result column="ban_reason" property="banReason"/>
        <result column="register_time" property="registerTime"/>
        <result column="u_update_time" property="updateTime"/>
        <result column="is_admin" property="isAdmin"/>
        <result column="last_login" property="lastLogin"/>
    </resultMap>

    <!-- 联合查询结果映射：PostWithUserDTO -->
    <resultMap id="PostWithUserResultMap" type="com.xju.codeduel.model.dto.PostWithUserDTO">
        <!-- 映射Posts对象 -->
        <association property="post" resultMap="BasePostsResultMap"/>
        <!-- 映射Users对象 -->
        <association property="user" resultMap="BaseUsersResultMap"/>
    </resultMap>

    <!-- 根据标题搜索帖子（包含用户信息） -->
    <select id="selectPostsWithUserByTitle" resultMap="PostWithUserResultMap">
        SELECT
            p.id as p_id,
            p.user_id,
            p.title,
            p.content,
            p.is_top,
            p.post_time,
            p.update_time,
            p.is_deleted,
            p.like_count,

            u.id as u_id,
            u.password,
            u.codeforces_id,
            u.avatar,
            u.rating,
            u.status,
            u.ban_reason,
            u.register_time,
            u.update_time as u_update_time,
            u.is_admin,
            u.last_login

        FROM posts p
                 LEFT JOIN users u ON p.user_id = u.id
        WHERE p.title LIKE CONCAT('%', #{title}, '%')
          AND p.is_deleted = 0
    </select>

    <!-- 根据标题搜索帖子（包含用户信息），支持分页，置顶优先 -->
    <select id="pageposts" resultMap="PostWithUserResultMap">
        SELECT
        p.id as p_id,
        p.user_id,
        p.title,
        p.content,
        p.is_top,
        p.post_time,
        p.update_time,
        p.is_deleted,
        p.like_count,

        u.id as u_id,
        u.password,
        u.codeforces_id,
        u.avatar,
        u.rating,
        u.status,
        u.ban_reason,
        u.register_time,
        u.update_time as u_update_time,
        u.is_admin,
        u.last_login
        FROM posts p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.is_deleted = 0
        <if test="title != null and title != ''">
            AND p.title LIKE CONCAT('%', #{title}, '%')
        </if>
        ORDER BY p.is_top DESC,  <!-- 置顶优先 -->
        p.post_time DESC <!-- 其次按发布时间降序 -->
    </select>

    <!-- 根据ID获取帖子详情（包含用户信息） -->
    <select id="getPostWithUserById" resultMap="PostWithUserResultMap">
        SELECT
            p.id as p_id,
            p.user_id,
            p.title,
            p.content,
            p.is_top,
            p.post_time,
            p.update_time,
            p.is_deleted,
            p.like_count,

            u.id as u_id,
            u.password,
            u.codeforces_id,
            u.avatar,
            u.rating,
            u.status,
            u.ban_reason,
            u.register_time,
            u.update_time as u_update_time,
            u.is_admin,
            u.last_login
        FROM posts p
                 LEFT JOIN users u ON p.user_id = u.id
        WHERE p.id = #{id} AND p.is_deleted = 0
    </select>

</mapper>