<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ced8d846-5362-481c-8125-7a083d509935" name="更改" comment="验证码服务" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="origin/master" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\maven\maven_rs" />
        <option name="userSettingsFile" value="D:\maven\settings-ali.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="PerforceDirect.Settings">
    <option name="CHARSET" value="无" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="302IQgRam4qofXhsxDft0UTJ7iP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.codeduelApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;zhangrun&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/22-9/大三/大三下/小学期实训/CodeDuel/code-fuel-backend/src/main/java/com/xju/codeduel/service/impl&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.CodeGenerator.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-backend\src\main\java\com\xju\codeduel\service\impl" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-backend" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-backend\src\main\java\com\xju\codeduel\common\utls" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-backend\src\main\java\com\xju\codeduel\config" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-backend\src\main\java\com\xju\codeduel" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.codeduelApplication">
    <configuration name="CodeGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xju.codeduel.CodeGenerator" />
      <module name="codeduel" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xju.codeduel.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="codeduelApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="codeduel" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xju.codeduel.codeduelApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.CodeGenerator" />
        <item itemvalue="应用程序.CodeGenerator" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ced8d846-5362-481c-8125-7a083d509935" name="更改" comment="" />
      <created>1752131169373</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752131169373</updated>
      <workItem from="1752819228834" duration="6717000" />
      <workItem from="1752887057558" duration="14521000" />
      <workItem from="1753232622555" duration="1495000" />
      <workItem from="1753340599277" duration="3462000" />
      <workItem from="1753351424303" duration="11215000" />
    </task>
    <task id="LOCAL-00001" summary="对战记录信息DTO">
      <option name="closed" value="true" />
      <created>1753002636043</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753002636043</updated>
    </task>
    <task id="LOCAL-00002" summary="对战记录信息DTO">
      <option name="closed" value="true" />
      <created>1753002794511</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753002794511</updated>
    </task>
    <task id="LOCAL-00003" summary="对战记录mapper层">
      <option name="closed" value="true" />
      <created>1753002987177</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753002987177</updated>
    </task>
    <task id="LOCAL-00004" summary="添加结果映射">
      <option name="closed" value="true" />
      <created>1753003017646</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753003017646</updated>
    </task>
    <task id="LOCAL-00005" summary="对战记录服务实现类">
      <option name="closed" value="true" />
      <created>1753003050506</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753003050506</updated>
    </task>
    <task id="LOCAL-00006" summary="对战记录服务实现类">
      <option name="closed" value="true" />
      <created>1753003137024</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753003137024</updated>
    </task>
    <task id="LOCAL-00007" summary="用户服务类">
      <option name="closed" value="true" />
      <created>1753003155477</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753003155477</updated>
    </task>
    <task id="LOCAL-00008" summary="添加啊获取首页用户数据">
      <option name="closed" value="true" />
      <created>1753003186881</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753003186881</updated>
    </task>
    <task id="LOCAL-00009" summary="添加获取信息">
      <option name="closed" value="true" />
      <created>1753003445038</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753003445038</updated>
    </task>
    <task id="LOCAL-00010">
      <option name="closed" value="true" />
      <created>1753013864512</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753013864512</updated>
    </task>
    <task id="LOCAL-00011" summary="实现注册后端">
      <option name="closed" value="true" />
      <created>1753013926473</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753013926473</updated>
    </task>
    <task id="LOCAL-00012">
      <option name="closed" value="true" />
      <created>1753013952947</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753013952947</updated>
    </task>
    <task id="LOCAL-00013">
      <option name="closed" value="true" />
      <created>1753014011922</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753014011922</updated>
    </task>
    <task id="LOCAL-00014" summary="CORS（跨源资源共享）配置类">
      <option name="closed" value="true" />
      <created>1753014051440</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753014051440</updated>
    </task>
    <task id="LOCAL-00015" summary="Codeforces用户信息数据传输对象（DTO）">
      <option name="closed" value="true" />
      <created>1753014094825</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753014094825</updated>
    </task>
    <task id="LOCAL-00016" summary="验证码服务">
      <option name="closed" value="true" />
      <created>1753014140269</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753014140269</updated>
    </task>
    <task id="LOCAL-00017" summary="验证码服务">
      <option name="closed" value="true" />
      <created>1753352596729</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753352596729</updated>
    </task>
    <option name="localTasksCounter" value="18" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="对战记录信息DTO" />
    <MESSAGE value="对战记录mapper层" />
    <MESSAGE value="添加结果映射" />
    <MESSAGE value="对战记录服务实现类" />
    <MESSAGE value="用户服务类" />
    <MESSAGE value="添加啊获取首页用户数据" />
    <MESSAGE value="添加获取信息" />
    <MESSAGE value="添加python服务" />
    <MESSAGE value="实现注册后端" />
    <MESSAGE value="密码加密" />
    <MESSAGE value="注册验证DTO" />
    <MESSAGE value="CORS（跨源资源共享）配置类" />
    <MESSAGE value="Codeforces用户信息数据传输对象（DTO）" />
    <MESSAGE value="验证码服务" />
    <option name="LAST_COMMIT_MESSAGE" value="验证码服务" />
  </component>
</project>