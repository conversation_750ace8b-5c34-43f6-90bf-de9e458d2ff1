package com.xju.codeduel.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.model.dto.PageDTO;
import com.xju.codeduel.model.dto.PostWithUserDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.converter.json.GsonBuilderUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.IPostsService;
import com.xju.codeduel.model.domain.Posts;

import java.time.LocalDateTime;
import java.util.List;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/posts")
public class PostsController {

    private final Logger logger = LoggerFactory.getLogger( PostsController.class );

    @Autowired
    private IPostsService postsService;


    /**
     * 描述：根据Id 查询帖子详情（包含用户信息）
     *
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("根据ID获取帖子详情（包含用户信息）")
    public JsonResponse<PostWithUserDTO> getById(@PathVariable("id") Long id)throws Exception {
        PostWithUserDTO postWithUser = postsService.getPostWithUserById(id);
        if (postWithUser == null) {
            return JsonResponse.failure("帖子不存在或已被删除");
        }
        return JsonResponse.success(postWithUser);
    }

    @GetMapping("/searchByTitle")
    @ApiOperation("根据标题搜索帖子（包含用户信息）")
    public JsonResponse<List<PostWithUserDTO>> searchPostsByTitle(
            @ApiParam("搜索标题关键词") @RequestParam String title) {
        List<PostWithUserDTO> result = postsService.getPostsWithUserByTitle(title);
        logger.info("搜索结果: {}", result);
        if(result.isEmpty()){
            System.out.println("没有搜索到相关帖子");
        }else{
            for(PostWithUserDTO postWithUserDTO:result){
                System.out.println(postWithUserDTO);
            }
        }
        return JsonResponse.success(result);
    }

    @GetMapping("/pagePosts")  // 路径保持大写 P
    @ApiOperation("分页获取帖子列表")
    public JsonResponse<Page<PostWithUserDTO>> pageposts(
            @ModelAttribute PageDTO pageDto,  // 使用 @ModelAttribute 绑定查询参数到对象
            @RequestParam(required = false) String title) {  // 单独绑定 title 参数
        // 调用服务层方法
        System.out.println(pageDto);
        System.out.println(title);
        Page<PostWithUserDTO> result = postsService.pageposts(pageDto, title);
        return JsonResponse.success(result);
    }

    @PostMapping("/create")
    @ApiOperation("创建新帖子")
    public JsonResponse<Posts> createPost(@RequestBody Posts posts) {
        // 前端记得校验数据

        // 2. 设置默认值（如发帖时间、初始点赞数等）
        posts.setPostTime(LocalDateTime.now());
        posts.setUpdateTime(LocalDateTime.now());
        posts.setLikeCount(0);
        posts.setIsTop(0); // 默认不置顶
        posts.setIsDeleted(0); // 默认未删除

        // 3. 调用 Service 保存数据
        boolean success = postsService.save(posts);
        if (!success) {
//            return JsonResponse.fail("创建帖子失败");
            System.out.println("创建帖子失败");
        }

        return JsonResponse.success(posts);

    }

    /**
     * 更新帖子
     * @param postId 帖子ID
     * @param posts 帖子对象
     * @param userId 当前用户ID
     * @param isAdmin 是否为管理员
     * @return 更新结果
     */
    @PutMapping("/{postId}")
    @ApiOperation(value = "更新帖子", notes = "管理员可以编辑任意帖子，普通用户只能编辑自己的帖子")
    public JsonResponse<Posts> updatePost(
            @ApiParam(value = "帖子ID", required = true)
            @PathVariable("postId") Long postId,
            @RequestBody Posts posts,
            @ApiParam(value = "当前用户ID", required = true)
            @RequestParam("userId") Long userId,
            @ApiParam(value = "是否为管理员", defaultValue = "false")
            @RequestParam(value = "isAdmin", defaultValue = "false") Boolean isAdmin) {

        try {
            logger.info("更新帖子请求，帖子ID: {}, 用户ID: {}, 是否管理员: {}", postId, userId, isAdmin);

            // 参数校验
            if (postId == null) {
                return JsonResponse.failure("帖子ID不能为空");
            }
            if (userId == null) {
                return JsonResponse.failure("用户ID不能为空");
            }
            if (posts.getTitle() == null || posts.getTitle().trim().isEmpty()) {
                return JsonResponse.failure("帖子标题不能为空");
            }
            if (posts.getContent() == null || posts.getContent().trim().isEmpty()) {
                return JsonResponse.failure("帖子内容不能为空");
            }

            // 查询帖子是否存在
            Posts existingPost = postsService.getById(postId);
            if (existingPost == null) {
                return JsonResponse.failure("帖子不存在");
            }

            // 权限检查：管理员可以编辑任意帖子，普通用户只能编辑自己的帖子
            if (!isAdmin && !existingPost.getUserId().equals(userId)) {
                return JsonResponse.failure("无权限编辑此帖子");
            }

            // 更新帖子信息
            existingPost.setTitle(posts.getTitle().trim());
            existingPost.setContent(posts.getContent().trim());
            existingPost.setUpdateTime(LocalDateTime.now());

            boolean success = postsService.updateById(existingPost);

            if (success) {
                logger.info("帖子更新成功，帖子ID: {}", postId);
                return JsonResponse.success(existingPost);
            } else {
                logger.error("帖子更新失败，帖子ID: {}", postId);
                return JsonResponse.failure("更新帖子失败");
            }

        } catch (Exception e) {
            logger.error("更新帖子失败，帖子ID: {}", postId, e);
            return JsonResponse.failure("更新帖子失败: " + e.getMessage());
        }
    }

    /**
     * 删除帖子
     * @param postId 帖子ID
     * @param userId 当前用户ID
     * @param isAdmin 是否为管理员
     * @return 删除结果
     */
    @DeleteMapping("/{postId}")
    @ApiOperation(value = "删除帖子", notes = "管理员可以删除任意帖子，普通用户只能删除自己的帖子")
    public JsonResponse<Boolean> deletePost(
            @ApiParam(value = "帖子ID", required = true)
            @PathVariable("postId") Long postId,
            @ApiParam(value = "当前用户ID", required = true)
            @RequestParam("userId") Long userId,
            @ApiParam(value = "是否为管理员", defaultValue = "false")
            @RequestParam(value = "isAdmin", defaultValue = "false") Boolean isAdmin) {

        try {
            logger.info("删除帖子请求，帖子ID: {}, 用户ID: {}, 是否管理员: {}", postId, userId, isAdmin);

            // 参数校验
            if (postId == null) {
                return JsonResponse.failure("帖子ID不能为空");
            }
            if (userId == null) {
                return JsonResponse.failure("用户ID不能为空");
            }

            // 查询帖子是否存在
            Posts post = postsService.getById(postId);
            if (post == null) {
                return JsonResponse.failure("帖子不存在");
            }

            // 权限检查：管理员可以删除任意帖子，普通用户只能删除自己的帖子
            if (!isAdmin && !post.getUserId().equals(userId)) {
                return JsonResponse.failure("无权限删除此帖子");
            }

            // 使用MyBatis-Plus的逻辑删除功能
            boolean success = postsService.removeById(postId);

            if (success) {
                logger.info("帖子删除成功，帖子ID: {}", postId);
                return JsonResponse.success(true);
            } else {
                logger.error("帖子删除失败，帖子ID: {}", postId);
                return JsonResponse.failure("删除帖子失败");
            }

        } catch (Exception e) {
            logger.error("删除帖子失败，帖子ID: {}", postId, e);
            return JsonResponse.failure("删除帖子失败: " + e.getMessage());
        }
    }

    /**
     * 切换帖子置顶状态
     * @param postId 帖子ID
     * @param userId 当前用户ID
     * @param isAdmin 是否为管理员
     * @return 操作结果
     */
    @PutMapping("/{postId}/pin")
    @ApiOperation(value = "切换帖子置顶状态", notes = "只有管理员可以置顶/取消置顶帖子")
    public JsonResponse<Posts> togglePinPost(
            @ApiParam(value = "帖子ID", required = true)
            @PathVariable("postId") Long postId,
            @ApiParam(value = "当前用户ID", required = true)
            @RequestParam("userId") Long userId,
            @ApiParam(value = "是否为管理员", defaultValue = "false")
            @RequestParam(value = "isAdmin", defaultValue = "false") Boolean isAdmin) {

        try {
            logger.info("切换帖子置顶状态请求，帖子ID: {}, 用户ID: {}, 是否管理员: {}", postId, userId, isAdmin);

            // 参数校验
            if (postId == null) {
                return JsonResponse.failure("帖子ID不能为空");
            }
            if (userId == null) {
                return JsonResponse.failure("用户ID不能为空");
            }

            // 权限检查：只有管理员可以置顶帖子
            if (!isAdmin) {
                return JsonResponse.failure("只有管理员可以置顶帖子");
            }

            // 查询帖子是否存在
            Posts post = postsService.getById(postId);
            if (post == null) {
                return JsonResponse.failure("帖子不存在");
            }

            // 切换置顶状态
            Integer currentTopStatus = post.getIsTop();
            Integer newTopStatus = (currentTopStatus == 1) ? 0 : 1;
            post.setIsTop(newTopStatus);
            post.setUpdateTime(LocalDateTime.now());

            boolean success = postsService.updateById(post);

            if (success) {
                String action = newTopStatus == 1 ? "置顶" : "取消置顶";
                logger.info("帖子{}成功，帖子ID: {}", action, postId);
                return JsonResponse.success(post);
            } else {
                logger.error("帖子置顶状态切换失败，帖子ID: {}", postId);
                return JsonResponse.failure("操作失败");
            }

        } catch (Exception e) {
            logger.error("切换帖子置顶状态失败，帖子ID: {}", postId, e);
            return JsonResponse.failure("操作失败: " + e.getMessage());
        }
    }


}

