# CodeDuel 个人中心实现详解

## 系统架构概述

个人中心是CodeDuel平台的核心功能模块，提供用户信息展示、Rating变化趋势分析、对战记录查看等功能。

```
前端(Vue3) ←→ Java后端(Spring Boot) ←→ MySQL数据库
     ↓              ↓                    ↓
   用户界面      业务逻辑处理          数据存储
```

## 技术栈

- **前端**: Vue 3 + Element Plus + ECharts + SCSS
- **后端**: Spring Boot + MyBatis Plus + MySQL
- **图表**: ECharts 5.x (专业级数据可视化)
- **样式**: SCSS + 响应式设计

## 核心功能模块

### 1. 用户搜索功能

#### 前端搜索组件 (`Profile.vue`)

```vue
<template>
  <!-- 搜索区域 -->
  <div class="search-header">
    <el-input
      v-model="searchUsername"
      placeholder="搜索用户（输入Codeforces用户名）"
      class="search-input"
      clearable
      @keyup.enter="searchUser"
    >
      <template #append>
        <el-button 
          :icon="Search" 
          @click="searchUser"
          :loading="searching"
          type="primary"
        >
          搜索
        </el-button>
      </template>
    </el-input>
  </div>
</template>

<script setup>
// 搜索用户方法
const searchUser = async () => {
  if (!searchUsername.value.trim()) {
    ElMessage.warning('请输入用户名')
    return
  }

  searching.value = true
  userNotFound.value = false

  try {
    // 调用API搜索用户
    const response = await getUserByUsername(searchUsername.value.trim())
    console.log('搜索用户API响应:', response)

    if (response.status && response.data && response.data.codeforcesId) {
      // 跳转到该用户的个人中心页面
      router.push(`/dashboard/profile/${searchUsername.value.trim()}`)
    } else {
      userNotFound.value = true
      userInfo.value = {}
      ElMessage.warning('未找到该用户')
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    userNotFound.value = true
    userInfo.value = {}
    ElMessage.error('搜索失败: ' + error.message)
  } finally {
    searching.value = false
  }
}
</script>
```

### 2. 用户信息展示

#### 个人信息卡片

```vue
<template>
  <!-- 个人信息卡片 -->
  <el-card v-if="!loading && !userNotFound && userInfo.codeforcesId" class="profile-header">
    <div class="profile-info">
      <!-- 头像区域 -->
      <div class="avatar-section">
        <el-avatar
          :size="120"
          :src="userInfo.avatar && userInfo.avatar !== '/' ? userInfo.avatar : defaultAvatar"
          @error="handleAvatarError"
        />
      </div>
      
      <!-- 用户详细信息 -->
      <div class="user-details">
        <h2>{{ userInfo.codeforcesId }}</h2>
        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-label">注册时间:</span>
            <span class="stat-value">{{ formatDate(userInfo.registerTime) }}</span>
          </div>
          <div class="stat-item" v-if="userInfo.lastLogin">
            <span class="stat-label">最近登录:</span>
            <span class="stat-value">{{ formatDate(userInfo.lastLogin) }}</span>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
// 头像错误处理
const handleAvatarError = () => {
  console.log('头像加载失败，使用默认头像')
}

// 日期格式化
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
```

### 3. Rating变化趋势图

#### ECharts图表实现

```javascript
// 创建现代化的Rating变化图表
const createRatingChart = () => {
  console.log('开始创建Rating图表')
  
  if (!ratingChart.value) {
    console.log('图表容器不存在')
    return
  }

  // 先清空容器
  ratingChart.value.innerHTML = ''

  // 销毁之前的图表实例
  if (chartInstance) {
    console.log('销毁之前的图表实例')
    chartInstance.dispose()
  }

  // 准备图表数据
  const chartData = prepareChartData()
  console.log('图表数据:', chartData)

  // 创建新的图表实例
  try {
    chartInstance = echarts.init(ratingChart.value)
    console.log('ECharts实例创建成功:', chartInstance)
  } catch (error) {
    console.error('ECharts初始化失败:', error)
    ratingChart.value.innerHTML = '<div style="text-align: center; padding: 50px; color: #f56c6c;">图表初始化失败</div>'
    return
  }

  // 美化的图表配置
  const option = {
    title: {
      text: 'Rating变化趋势',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e1e8ed',
      borderWidth: 1,
      textStyle: {
        color: '#2c3e50'
      },
      formatter: function(params) {
        const point = params[0]
        const dataIndex = point.dataIndex
        
        if (dataIndex === 0) {
          return `<div style="text-align: left;">
            <div><strong>初始Rating</strong></div>
            <div>Rating: ${point.value}</div>
          </div>`
        } else if (ratingHistory.value.length > 0) {
          const sortedHistories = [...ratingHistory.value].sort((a, b) =>
            new Date(a.recordTime) - new Date(b.recordTime)
          )
          const record = sortedHistories[dataIndex - 1]

          if (record) {
            const change = record.newRating - record.oldRating
            const changeText = change > 0 ? `+${change}` : `${change}`
            const changeColor = change > 0 ? '#67C23A' : change < 0 ? '#F56C6C' : '#909399'

            return `<div style="text-align: left;">
              <div><strong>${formatDate(record.recordTime)}</strong></div>
              <div>Rating: ${record.oldRating} → ${record.newRating}</div>
              <div>变化: <span style="color: ${changeColor}; font-weight: bold;">${changeText}</span></div>
              <div>结果: ${record.reason}</div>
            </div>`
          }
        }
        return `Rating: ${point.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.xAxisData,
      axisLine: {
        lineStyle: {
          color: '#e1e8ed'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: 'Rating',
      nameTextStyle: {
        color: '#666',
        fontSize: 14
      },
      axisLine: {
        lineStyle: {
          color: '#e1e8ed'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5',
          type: 'dashed'
        }
      }
    },
    series: [{
      name: 'Rating',
      type: 'line',
      data: chartData.seriesData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        width: 3,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: '#67C23A' }
          ]
        }
      },
      itemStyle: {
        color: '#409EFF',
        borderColor: '#fff',
        borderWidth: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
          ]
        }
      }
    }]
  }

  try {
    chartInstance.setOption(option)
    console.log('图表配置已设置完成')

    // 响应式调整
    window.addEventListener('resize', () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    })

    console.log('Rating图表创建完成')
  } catch (error) {
    console.error('设置图表配置失败:', error)
    ratingChart.value.innerHTML = '<div style="text-align: center; padding: 50px; color: #f56c6c;">图表配置失败</div>'
  }
}

// 准备图表数据
const prepareChartData = () => {
  const xAxisData = ['初始Rating']
  const seriesData = [1500] // 第一个点固定为1500

  // 如果有历史记录，添加历史数据
  if (ratingHistory.value.length > 0) {
    // 按时间排序
    const sortedHistories = [...ratingHistory.value].sort((a, b) =>
      new Date(a.recordTime) - new Date(b.recordTime)
    )

    // 添加每次对战后的Rating
    sortedHistories.forEach(record => {
      xAxisData.push(formatDateShort(record.recordTime))
      seriesData.push(record.newRating)
    })
  }

  return { xAxisData, seriesData }
}

// 格式化日期（短格式）
const formatDateShort = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
```

### 4. 对战统计模块

#### 统计数据计算

```javascript
// 对战统计
const battleStats = ref({
  totalBattles: 0,
  wins: 0,
  winRate: 0
})

// 计算对战统计
const calculateBattleStats = () => {
  if (ratingHistory.value.length === 0) {
    resetBattleStats()
    return
  }

  const totalBattles = ratingHistory.value.length
  const wins = ratingHistory.value.filter(record =>
    record.newRating > record.oldRating
  ).length
  const winRate = totalBattles > 0 ? Math.round((wins / totalBattles) * 100) : 0

  battleStats.value = {
    totalBattles,
    wins,
    winRate
  }

  console.log('对战统计计算完成:', battleStats.value)
}

// 重置对战统计
const resetBattleStats = () => {
  battleStats.value = {
    totalBattles: 0,
    wins: 0,
    winRate: 0
  }
}
```

#### 统计卡片UI

```vue
<template>
  <!-- 对战统计 -->
  <el-card class="battle-stats-card" header="对战统计">
    <div class="stats-grid">
      <div class="stat-item">
        <div class="stat-value">{{ battleStats.totalBattles }}</div>
        <div class="stat-label">总对战数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value win">{{ battleStats.wins }}</div>
        <div class="stat-label">胜利场次</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ battleStats.winRate }}%</div>
        <div class="stat-label">胜率</div>
      </div>
    </div>
  </el-card>
</template>
```

### 5. 对战记录表格

#### 记录生成与展示

```javascript
// 全部对战记录
const allBattleRecords = ref([])

// 生成全部对战记录
const generateBattleRecords = () => {
  if (ratingHistory.value.length === 0) {
    allBattleRecords.value = []
    return
  }

  // 按时间排序，最新的在前
  const sortedHistories = [...ratingHistory.value].sort((a, b) =>
    new Date(b.recordTime) - new Date(a.recordTime)
  )

  // 生成对战记录格式
  allBattleRecords.value = sortedHistories.map(record => {
    const ratingChange = record.newRating - record.oldRating
    const result = ratingChange > 0 ? 'win' : ratingChange < 0 ? 'lose' : 'draw'

    return {
      id: record.id,
      battleId: record.battleId,
      problemTitle: `对战 #${record.battleId}`,
      startTime: new Date(record.recordTime),
      result: result,
      ratingChange: ratingChange,
      reason: record.reason,
      oldRating: record.oldRating,
      newRating: record.newRating
    }
  })

  console.log('生成的全部对战记录:', allBattleRecords.value.length)
}
```

#### 对战记录表格UI

```vue
<template>
  <!-- 全部对战记录 -->
  <el-card v-if="!loading && !userNotFound && userInfo.codeforcesId" class="all-battles-card">
    <template #header>
      <h3>全部对战记录</h3>
    </template>
    <el-table :data="allBattleRecords" style="width: 100%" empty-text="暂无对战记录">
      <el-table-column prop="battleId" label="对战ID" width="100" align="center" />
      <el-table-column label="时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column label="Rating变化" width="200" align="center">
        <template #default="{ row }">
          <div class="rating-change-cell">
            <span class="old-rating">{{ row.oldRating }}</span>
            <span class="arrow">→</span>
            <span class="new-rating">{{ row.newRating }}</span>
            <span
              class="change-value"
              :class="{
                'positive': row.ratingChange > 0,
                'negative': row.ratingChange < 0,
                'neutral': row.ratingChange === 0
              }"
            >
              {{ row.ratingChange > 0 ? '+' : '' }}{{ row.ratingChange }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="结果" width="120" align="center">
        <template #default="{ row }">
          <el-tag
            :type="row.result === 'win' ? 'success' : row.result === 'lose' ? 'danger' : 'info'"
            size="small"
          >
            {{ row.result === 'win' ? '胜利' : row.result === 'lose' ? '失败' : '平局' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="reason" label="详情" min-width="150" />
    </el-table>
  </el-card>
</template>
```

## 后端API实现

### 1. 用户信息获取API

#### Controller层 (`UsersController.java`)

```java
/**
 * 根据用户名获取用户信息
 */
@RequestMapping(value = "/profile/{username}", method = RequestMethod.GET)
@ResponseBody
public JsonResponse<Users> getUserByUsername(@PathVariable("username") String username) {
    Users user = usersService.getUserByUsername(username);
    return JsonResponse.success(user);
}

/**
 * 获取用户详细Rating历史记录
 */
@GetMapping("rating-history/{username}")
@ResponseBody
public JsonResponse getUserRatingHistoryDetail(@PathVariable String username) {

    if (username == null || username.trim().isEmpty()) {
        return JsonResponse.failure("用户名不能为空");
    }

    UserRatingChangeDTO result = usersService.getUserRatingHistoryByUsername(username.trim());

    if (result == null) {
        return JsonResponse.failure("用户不存在");
    }

    return JsonResponse.success(result);
}
```

### 2. Service层实现

#### UsersServiceImpl.java

```java
@Override
public Users getUserByUsername(String username) {
    LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(Users::getCodeforcesId, username);
    return usersMapper.selectOne(queryWrapper);
}

@Override
public UserRatingChangeDTO getUserRatingHistoryByUsername(String username) {
    List<UserRatingChangeDTO> results = usersMapper.getUserRatingHistoryByUsername(username);

    if (results == null || results.isEmpty()) {
        return null;
    }

    // 合并结果：取第一个作为基础，合并所有的历史记录
    UserRatingChangeDTO mergedResult = new UserRatingChangeDTO();
    mergedResult.setUser(results.get(0).getUser());

    // 收集所有的历史记录
    List<UserRatingHistories> allHistories = new ArrayList<>();
    for (UserRatingChangeDTO result : results) {
        if (result.getRatingHistories() != null && !result.getRatingHistories().isEmpty()) {
            allHistories.addAll(result.getRatingHistories());
        }
    }

    mergedResult.setRatingHistories(allHistories);
    return mergedResult;
}
```

### 3. 数据传输对象 (DTO)

#### UserRatingChangeDTO.java

```java
@Data
@ApiModel(value = "UserRatingHistoryDTO对象", description = "用户Rating历史记录DTO")
public class UserRatingChangeDTO {
    @ApiModelProperty(value = "用户信息")
    private Users user;

    @ApiModelProperty(value = "Rating变化历史记录(按时间倒序)")
    private List<UserRatingHistories> ratingHistories;
}
```

### 4. MyBatis映射配置

#### UsersMapper.xml

```xml
<!-- 用户基本信息映射 -->
<resultMap id="userResultMap" type="com.xju.codeduel.model.domain.Users">
    <id property="id" column="id"/>
    <result property="codeforcesId" column="codeforces_id"/>
    <result property="password" column="password"/>
    <result property="avatar" column="avatar"/>
    <result property="rating" column="rating"/>
    <result property="registerTime" column="register_time"/>
    <result property="lastLogin" column="last_login"/>
    <result property="isAdmin" column="is_admin"/>
</resultMap>

<!-- Rating历史记录映射 -->
<resultMap id="ratingHistoryResultMap" type="com.xju.codeduel.model.domain.UserRatingHistories">
    <id property="id" column="rh_id"/>
    <result property="userId" column="user_id"/>
    <result property="battleId" column="battle_id"/>
    <result property="oldRating" column="old_rating"/>
    <result property="newRating" column="new_rating"/>
    <result property="reason" column="reason"/>
    <result property="recordTime" column="record_time"/>
</resultMap>

<!-- 复合结果映射 -->
<resultMap id="userRatingChangeResultMap" type="com.xju.codeduel.model.dto.UserRatingChangeDTO">
    <association property="user" resultMap="userResultMap"/>
    <collection property="ratingHistories" resultMap="ratingHistoryResultMap"
                ofType="com.xju.codeduel.model.domain.UserRatingHistories"/>
</resultMap>

<!-- 根据用户名精确查询用户及其完整Rating历史记录 -->
<select id="getUserRatingHistoryByUsername" resultMap="userRatingChangeResultMap">
    SELECT
    <include refid="userColumns"/>,
    <include refid="ratingHistoryColumns"/>
    FROM
    users u
    LEFT JOIN
    user_rating_histories rh ON u.id = rh.user_id
    WHERE
    u.codeforces_id = #{username}
    ORDER BY
    rh.record_time DESC
</select>
```

## 前端API调用

### 1. API接口定义 (`api/api.js`)

```javascript
import request from "@/utils/request";

// 根据用户名获取用户信息
export const getUserByUsername = username => request.get(`/api/users/profile/${username}`);

// 获取用户详细历史记录（用于详情查看）
export const getUserDetailHistory = username => request.get(`/api/users/rating-history/${username}`);
```

### 2. 数据加载逻辑

```javascript
// 加载用户信息方法
const loadUserInfo = async (username) => {
  if (!username) return

  userNotFound.value = false

  try {
    const response = await getUserByUsername(username)
    if (response.data) {
      userInfo.value = response.data
      searchUsername.value = username
    } else {
      userNotFound.value = true
      userInfo.value = {}
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    userNotFound.value = true
    userInfo.value = {}
  }
}

// 加载用户完整资料
const loadUserProfile = async (username) => {
  if (!username) return

  loading.value = true
  userNotFound.value = false

  try {
    // 获取用户基本信息
    const userResponse = await getUserByUsername(username)
    console.log('用户信息API响应:', userResponse)

    if (userResponse.status && userResponse.data && userResponse.data.codeforcesId) {
      userInfo.value = userResponse.data

      // 获取Rating历史
      await loadRatingHistory(username)

      // 创建图表
      await nextTick()
      console.log('准备创建图表，等待DOM更新完成')
      setTimeout(() => {
        try {
          createRatingChart()
        } catch (error) {
          console.error('创建图表失败:', error)
        }
      }, 100)
    } else {
      userNotFound.value = true
      userInfo.value = {}
      ratingHistory.value = []
      ElMessage.warning('未找到该用户')
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    userNotFound.value = true
    userInfo.value = {}
    ratingHistory.value = []
    ElMessage.error('加载用户信息失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载Rating历史数据
const loadRatingHistory = async (username) => {
  try {
    const response = await getUserDetailHistory(username)
    if (response.status && response.data && response.data.ratingHistories) {
      ratingHistory.value = response.data.ratingHistories
      console.log(`获取到用户 ${username} 的 ${ratingHistory.value.length} 条历史记录`)

      // 计算对战统计
      calculateBattleStats()

      // 生成对战记录
      generateBattleRecords()
    } else {
      ratingHistory.value = []
      allBattleRecords.value = []
      resetBattleStats()
    }
  } catch (error) {
    console.error('获取Rating历史失败:', error)
    ratingHistory.value = []
    allBattleRecords.value = []
    resetBattleStats()
  }
}
```

### 3. 路由监听与生命周期

```javascript
// 监听路由参数变化
watch(() => route.params.username, (newUsername) => {
  if (newUsername) {
    loadUserInfo(newUsername)
  }
}, { immediate: true })

// 组件挂载
onMounted(async () => {
  console.log('Profile组件已挂载')
  try {
    await loadUserFromRoute()
  } catch (error) {
    console.error('onMounted错误:', error)
    loading.value = false
  }
})

// 监听路由变化
watch(() => route.params.username, async (newUsername) => {
  console.log('路由参数变化:', newUsername)
  if (newUsername) {
    try {
      await loadUserProfile(newUsername)
    } catch (error) {
      console.error('路由变化处理错误:', error)
      loading.value = false
    }
  }
})
```

## 样式设计 (SCSS)

### 1. 主容器样式

```scss
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  // 搜索区域样式
  .search-header {
    margin-bottom: 25px;

    .search-input {
      max-width: 500px;
      margin: 0 auto;
      display: block;

      .el-input-group__append {
        background-color: #409EFF;
        border-color: #409EFF;
        color: white;
      }
    }
  }

  // 个人信息卡片样式
  .profile-header {
    margin-bottom: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .profile-info {
      display: flex;
      align-items: center;
      gap: 30px;

      .avatar-section {
        flex-shrink: 0;

        .el-avatar {
          border: 4px solid #f0f0f0;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .user-details {
        flex: 1;

        h2 {
          margin: 0 0 15px 0;
          color: #2c3e50;
          font-size: 28px;
          font-weight: bold;
        }

        .user-stats {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .stat-item {
            display: flex;
            align-items: center;

            .stat-label {
              color: #666;
              margin-right: 10px;
              min-width: 80px;
            }

            .stat-value {
              color: #2c3e50;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}
```

### 2. 图表和统计样式

```scss
// 内容区域样式
.content-section {
  margin-bottom: 25px;

  // Rating图表卡片
  .chart-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .chart-container {
      height: 400px;
      width: 100%;
      position: relative;
    }
  }

  // 对战统计卡片
  .battle-stats-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .stats-grid {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .stat-item {
        text-align: center;
        padding: 15px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #2c3e50;
          margin-bottom: 5px;

          &.win {
            color: #67C23A;
          }
        }

        .stat-label {
          font-size: 12px;
          color: #666;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }
}
```

### 3. 对战记录表格样式

```scss
// 对战记录卡片
.all-battles-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .rating-change-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

    .old-rating, .new-rating {
      font-weight: 500;
      color: #2c3e50;
    }

    .arrow {
      color: #909399;
      font-weight: bold;
    }

    .change-value {
      font-weight: bold;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;

      &.positive {
        color: #67C23A;
        background-color: rgba(103, 194, 58, 0.1);
      }

      &.negative {
        color: #F56C6C;
        background-color: rgba(245, 108, 108, 0.1);
      }

      &.neutral {
        color: #909399;
        background-color: rgba(144, 147, 153, 0.1);
      }
    }
  }
}
```

### 4. 响应式设计

```scss
// 响应式设计
@media (max-width: 1200px) {
  .profile-container {
    padding: 15px;

    .content-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-container {
    .profile-header {
      .profile-info {
        flex-direction: column;
        text-align: center;
        gap: 20px;

        .user-details {
          .user-stats {
            align-items: center;

            .stat-item {
              justify-content: center;
            }
          }
        }
      }
    }

    .content-section {
      .el-col {
        span: 24; // 在小屏幕上堆叠显示
      }

      .chart-card {
        .chart-container {
          height: 300px; // 减小图表高度
        }
      }

      .battle-stats-card {
        .stats-grid {
          flex-direction: row;
          justify-content: space-around;

          .stat-item {
            flex: 1;
            margin: 0 5px;
          }
        }
      }
    }
  }
}
```

## 核心功能特性

### 1. 智能搜索功能
- **实时搜索**: 支持按Enter键或点击搜索按钮
- **用户验证**: 自动验证用户是否存在
- **路由跳转**: 搜索成功后自动跳转到用户个人页面
- **错误处理**: 完善的错误提示和异常处理

### 2. 动态Rating趋势图
- **ECharts集成**: 使用专业级图表库实现数据可视化
- **交互式图表**: 支持鼠标悬停查看详细信息
- **渐变效果**: 美观的线条和区域渐变色彩
- **响应式设计**: 自动适应不同屏幕尺寸
- **数据处理**: 智能处理初始Rating(1500)和历史变化

### 3. 对战统计分析
- **实时计算**: 根据Rating历史自动计算统计数据
- **多维度统计**: 总对战数、胜利场次、胜率等
- **视觉化展示**: 卡片式布局，数据一目了然
- **动态更新**: 数据变化时自动重新计算

### 4. 详细对战记录
- **完整记录**: 显示所有历史对战信息
- **时间排序**: 按时间倒序显示，最新记录在前
- **Rating变化**: 直观显示每场对战的Rating变化
- **结果标识**: 用不同颜色标识胜负结果
- **表格展示**: 使用Element Plus表格组件

### 5. 头像处理机制
- **默认头像**: 当用户头像不存在时显示默认头像
- **错误处理**: 头像加载失败时的降级处理
- **多尺寸支持**: 不同场景使用不同尺寸的头像

## 技术亮点

### 1. 前端技术亮点

#### Vue 3 Composition API
```javascript
// 使用最新的Composition API，代码更简洁、逻辑更清晰
import { ref, onMounted, nextTick, watch } from 'vue'

// 响应式数据管理
const userInfo = ref({})
const ratingHistory = ref([])
const battleStats = ref({
  totalBattles: 0,
  wins: 0,
  winRate: 0
})
```

#### ECharts专业图表
```javascript
// 专业级数据可视化
import * as echarts from 'echarts'

// 渐变色配置
lineStyle: {
  width: 3,
  color: {
    type: 'linear',
    x: 0, y: 0, x2: 1, y2: 0,
    colorStops: [
      { offset: 0, color: '#409EFF' },
      { offset: 1, color: '#67C23A' }
    ]
  }
}
```

#### 响应式设计
```scss
// 移动端适配
@media (max-width: 768px) {
  .profile-container {
    .content-section {
      .el-col {
        span: 24; // 堆叠显示
      }
    }
  }
}
```

### 2. 后端技术亮点

#### MyBatis Plus高效查询
```xml
<!-- 复杂关联查询，一次性获取用户信息和Rating历史 -->
<select id="getUserRatingHistoryByUsername" resultMap="userRatingChangeResultMap">
    SELECT
    <include refid="userColumns"/>,
    <include refid="ratingHistoryColumns"/>
    FROM users u
    LEFT JOIN user_rating_histories rh ON u.id = rh.user_id
    WHERE u.codeforces_id = #{username}
    ORDER BY rh.record_time DESC
</select>
```

#### DTO数据传输优化
```java
// 合并多条记录，优化数据传输
UserRatingChangeDTO mergedResult = new UserRatingChangeDTO();
mergedResult.setUser(results.get(0).getUser());

List<UserRatingHistories> allHistories = new ArrayList<>();
for (UserRatingChangeDTO result : results) {
    if (result.getRatingHistories() != null && !result.getRatingHistories().isEmpty()) {
        allHistories.addAll(result.getRatingHistories());
    }
}
mergedResult.setRatingHistories(allHistories);
```

### 3. 性能优化

#### 前端性能优化
- **懒加载**: 图表只在需要时创建
- **防抖处理**: 搜索输入防抖，避免频繁请求
- **内存管理**: 组件销毁时清理图表实例
- **缓存机制**: 合理使用Vue的响应式缓存

#### 后端性能优化
- **SQL优化**: 使用LEFT JOIN一次性获取关联数据
- **索引优化**: 在codeforces_id字段上建立索引
- **数据分页**: 支持大数据量的分页查询

## 数据库设计

### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    codeforces_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'Codeforces用户名',
    password VARCHAR(255) NOT NULL COMMENT '加密密码',
    avatar VARCHAR(500) COMMENT '头像URL',
    rating INT DEFAULT 1500 COMMENT '当前Rating',
    register_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    last_login DATETIME COMMENT '最后登录时间',
    is_admin TINYINT DEFAULT 0 COMMENT '是否管理员'
);
```

### 2. Rating历史表 (user_rating_histories)
```sql
CREATE TABLE user_rating_histories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    battle_id BIGINT NOT NULL COMMENT '对战ID',
    old_rating INT NOT NULL COMMENT '对战前Rating',
    new_rating INT NOT NULL COMMENT '对战后Rating',
    reason VARCHAR(200) COMMENT '变化原因',
    record_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_record_time (record_time)
);
```

## 实现的功能总结

### 1. 用户搜索与展示
- ✅ 支持按Codeforces用户名搜索
- ✅ 显示用户基本信息（头像、用户名、注册时间等）
- ✅ 用户不存在时的友好提示
- ✅ 搜索结果的路由跳转

### 2. Rating变化可视化
- ✅ 专业级ECharts图表展示Rating趋势
- ✅ 交互式图表，支持悬停查看详情
- ✅ 美观的渐变色和动画效果
- ✅ 响应式图表，适配不同屏幕

### 3. 对战数据统计
- ✅ 自动计算总对战数、胜利场次、胜率
- ✅ 实时统计数据更新
- ✅ 卡片式统计展示
- ✅ 数据可视化优化

### 4. 对战记录管理
- ✅ 完整的对战历史记录表格
- ✅ Rating变化的直观显示
- ✅ 胜负结果的颜色标识
- ✅ 按时间排序的记录展示

### 5. 用户体验优化
- ✅ 加载状态提示
- ✅ 错误处理和友好提示
- ✅ 响应式设计，支持移动端
- ✅ 现代化UI设计

## 技术架构优势

1. **前后端分离**: Vue3前端 + Spring Boot后端，职责清晰
2. **组件化开发**: 可复用的Vue组件，维护性强
3. **数据可视化**: ECharts专业图表库，展示效果佳
4. **响应式设计**: 适配多种设备，用户体验好
5. **性能优化**: 合理的缓存和懒加载策略
6. **代码规范**: 统一的代码风格和注释规范

这个个人中心模块展示了现代Web应用的完整实现流程，从前端UI设计到后端API开发，从数据库设计到性能优化，体现了全栈开发的技术水平和工程实践能力。
