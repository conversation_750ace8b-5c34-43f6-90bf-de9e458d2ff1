<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ChatDotRound,
  EditPen,
  Search,
  ArrowUp,
  MoreFilled,
  Promotion,
  View,
  Grid
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPostsList, createPost as createPostAPI, updatePost as updatePostAPI, deletePost as deletePostAPI, togglePinPost as togglePinPostAPI, getInfo } from '@/api/api'

const router = useRouter()

// 响应式数据定义
const loading = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const searchKeyword = ref('')
const activeSearchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(5)
const totalPosts = ref(0)
const editingPost = ref(null)

// Markdown 编辑器相关
const activeTab = ref('edit') // edit, preview, split
const contentEditor = ref()

// 创建帖子表单
const createForm = ref({
  title: '',
  content: ''
})

// 编辑帖子表单
const editForm = ref({
  title: '',
  content: ''
})

const createRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
}

const editRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
}

const createFormRef = ref()
const editFormRef = ref()

// 帖子数据
const pinnedPosts = ref([]) // 置顶帖子
const posts = ref([]) // 普通帖子列表

// 当前用户信息
const currentUser = ref(null)

// 过滤和排序帖子列表
const filteredPosts = computed(() => {
  let filtered = posts.value

  // 按标题进行模糊搜索
  if (activeSearchKeyword.value) {
    filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(activeSearchKeyword.value.toLowerCase())
    )
  }

  // 按发布时间排序（最新在前）
  filtered.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime))

  return filtered
})

// Markdown 相关计算属性
const previewContent = computed(() => {
  return renderMarkdown(createForm.value.content || '')
})

const wordCount = computed(() => {
  return createForm.value.content ? createForm.value.content.length : 0
})

const canPublish = computed(() => {
  return createForm.value.title?.trim() && createForm.value.content?.trim()
})

const canUpdate = computed(() => {
  return editForm.value.title?.trim() && editForm.value.content?.trim()
})

// 编辑模式的预览内容
const editPreviewContent = computed(() => {
  return renderMarkdown(editForm.value.content || '')
})

// 编辑模式的字数统计
const editWordCount = computed(() => {
  return editForm.value.content ? editForm.value.content.length : 0
})

// 简单的 Markdown 渲染函数
const renderMarkdown = (text) => {
  if (!text) return ''

  let html = text
      // 代码块（需要在其他替换之前处理）
      .replace(/```([^`]+)```/gim, '<pre><code>$1</code></pre>')
      // 标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // 粗体和斜体
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      // 行内代码
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank">$1</a>')

  // 处理列表
  const lines = html.split('\n')
  const processedLines = []
  let inList = false

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    if (line.startsWith('- ')) {
      if (!inList) {
        processedLines.push('<ul>')
        inList = true
      }
      processedLines.push(`<li>${line.substring(2)}</li>`)
    } else if (line.match(/^\d+\. /)) {
      if (!inList) {
        processedLines.push('<ol>')
        inList = true
      }
      processedLines.push(`<li>${line.replace(/^\d+\. /, '')}</li>`)
    } else {
      if (inList) {
        processedLines.push('</ul>')
        inList = false
      }
      if (line) {
        processedLines.push(line)
      } else {
        processedLines.push('<br>')
      }
    }
  }

  if (inList) {
    processedLines.push('</ul>')
  }

  return processedLines.join('')
}

// 方法
const formatTime = (time) => {
  const now = new Date()
  const diff = now - new Date(time)
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  const days = Math.floor(hours / 24)
  if (days < 7) return `${days}天前`
  return new Date(time).toLocaleDateString()
}

const viewPost = (postId) => {
  router.push(`/dashboard/forum/post/${postId}`)
}

const canManagePost = (post) => {
  // 如果没有当前用户信息，不显示管理操作
  if (!currentUser.value) {
    return false
  }

  // 管理员可以管理所有帖子
  if (currentUser.value.isAdmin) {
    return true
  }

  // 普通用户只能管理自己的帖子
  return Number(post.author.id) === Number(currentUser.value.id)
}

// 检查是否可以置顶帖子（只有管理员可以）
const canPinPost = () => {
  return currentUser.value && currentUser.value.isAdmin
}

const handleSearch = () => {
  activeSearchKeyword.value = searchKeyword.value.trim()
  currentPage.value = 1
  loadPosts()
}

// 获取当前用户信息
const loadCurrentUser = async () => {
  try {
    const response = await getInfo()
    console.log('用户信息API响应:', response)
    if (response.status) {
      currentUser.value = response.data
      console.log('当前用户信息:', currentUser.value)
    } else {
      console.warn('获取用户信息失败:', response.message)
      // 如果获取用户信息失败，使用默认用户ID（临时方案）
      currentUser.value = { id: 1, codeforcesId: 'guest', isAdmin: false }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 如果获取用户信息失败，使用默认用户ID（临时方案）
    currentUser.value = { id: 1, codeforcesId: 'guest', isAdmin: false }
  }
}

// 从后端获取帖子数据
const loadPosts = async () => {
  try {
    loading.value = true

    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      title: activeSearchKeyword.value || undefined // 如果为空则不传递
    }

    console.log('请求参数:', params)
    const response = await getPostsList(params)
    console.log('后端响应:', response)

    if (response.status) {
      const data = response.data

      // 处理帖子数据，转换为前端需要的格式
      // 注意：后端的 likeCount 字段实际上代表评论数量
      const processedPosts = data.records.map(item => ({
        id: item.post.id,
        title: item.post.title,
        content: item.post.content,
        isTop: item.post.isTop,
        publishTime: new Date(item.post.postTime),
        likeCount: 0, // 暂时没有点赞功能，设为0
        commentCount: item.post.likeCount, // 后端的 likeCount 实际上是评论数量
        author: {
          id: item.user.id,
          username: item.user.codeforcesId,
          avatar: item.user.avatar,
          rating: item.user.rating
        }
      }))

      // 分离置顶帖子和普通帖子
      pinnedPosts.value = processedPosts.filter(post => post.isTop === 1)
      posts.value = processedPosts.filter(post => post.isTop === 0)

      // 更新分页信息
      totalPosts.value = data.total

      console.log('置顶帖子:', pinnedPosts.value)
      console.log('普通帖子:', posts.value)
      console.log('总数:', totalPosts.value)
    } else {
      ElMessage.error(response.message || '获取帖子列表失败')
    }
  } catch (error) {
    console.error('获取帖子列表失败:', error)
    ElMessage.error('获取帖子列表失败')
  } finally {
    loading.value = false
  }
}

const handlePostAction = (command) => {
  const [action, postId] = command.split('-')

  switch (action) {
    case 'edit':
      handleEditPost(postId)
      break
    case 'pin':
      handleTogglePin(postId, false) // false表示当前不是置顶状态，要置顶
      break
    case 'unpin':
      handleTogglePin(postId, true) // true表示当前是置顶状态，要取消置顶
      break
    case 'delete':
      handleDeletePost(postId)
      break
  }
}

// 处理编辑帖子
const handleEditPost = (postId) => {
  // 查找要编辑的帖子
  const allPosts = [...pinnedPosts.value, ...posts.value]
  const post = allPosts.find(p => p.id == postId)

  if (!post) {
    ElMessage.error('帖子不存在')
    return
  }

  // 设置编辑的帖子和表单数据
  editingPost.value = post
  editForm.value = {
    title: post.title,
    content: post.content
  }

  // 显示编辑对话框
  showEditDialog.value = true
}

// 处理置顶/取消置顶帖子
const handleTogglePin = async (postId, isCurrentlyPinned) => {
  try {
    // 检查管理员权限
    if (!canPinPost()) {
      ElMessage.error('只有管理员可以置顶帖子')
      return
    }

    loading.value = true

    const params = {
      userId: Number(currentUser.value.id),
      isAdmin: Boolean(currentUser.value.isAdmin)
    }

    const response = await togglePinPostAPI(postId, params)

    if (response.status) {
      const action = isCurrentlyPinned ? '取消置顶' : '置顶'
      ElMessage.success(`帖子${action}成功`)
      // 重新加载帖子列表
      await loadPosts()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('置顶操作失败:', error)
    ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 处理删除帖子
const handleDeletePost = (postId) => {
  // 检查用户权限
  if (!currentUser.value) {
    ElMessage.error('请先登录')
    return
  }

  ElMessageBox.confirm('确定要删除这个帖子吗？', '确认删除', {
    type: 'warning',
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(async () => {
    try {
      loading.value = true

      const params = {
        userId: Number(currentUser.value.id),
        isAdmin: Boolean(currentUser.value.isAdmin)
      }

      const response = await deletePostAPI(postId, params)

      if (response.status) {
        ElMessage.success('帖子删除成功')
        // 重新加载帖子列表
        await loadPosts()
      } else {
        ElMessage.error(response.message || '删除帖子失败')
      }
    } catch (error) {
      console.error('删除帖子失败:', error)
      ElMessage.error('删除帖子失败: ' + (error.response?.data?.message || error.message))
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 更新帖子
const updatePost = async () => {
  editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 确保有用户ID和编辑的帖子
        if (!currentUser.value?.id) {
          ElMessage.error('请先登录后再编辑帖子')
          return
        }

        if (!editingPost.value) {
          ElMessage.error('编辑的帖子信息丢失')
          return
        }

        const postData = {
          title: editForm.value.title.trim(),
          content: editForm.value.content.trim()
        }

        const params = {
          userId: Number(currentUser.value.id),
          isAdmin: Boolean(currentUser.value.isAdmin)
        }

        // 验证数据
        if (!postData.title || !postData.content) {
          ElMessage.error('标题和内容不能为空')
          return
        }

        console.log('更新帖子数据:', postData)
        console.log('更新帖子参数:', params)
        const response = await updatePostAPI(editingPost.value.id, postData, params)
        console.log('更新帖子响应:', response)

        if (response.status) {
          ElMessage.success('帖子更新成功！')
          showEditDialog.value = false
          editForm.value = { title: '', content: '' }
          editingPost.value = null
          activeTab.value = 'edit'

          // 重新加载帖子列表
          await loadPosts()
        } else {
          ElMessage.error(response.message || '更新帖子失败')
        }
      } catch (error) {
        console.error('更新帖子失败:', error)
        console.error('错误状态码:', error.response?.status)
        console.error('错误详情:', error.response?.data)

        let errorMessage = '更新帖子失败'
        if (error.response?.data?.message) {
          errorMessage += ': ' + error.response.data.message
        } else if (error.response?.status === 500) {
          errorMessage += ': 服务器内部错误，请检查数据格式或联系管理员'
        } else {
          errorMessage += ': ' + error.message
        }

        ElMessage.error(errorMessage)
      } finally {
        loading.value = false
      }
    }
  })
}

// 创建新帖子

// Markdown 编辑器功能
const insertMarkdown = (markdown) => {
  const textarea = contentEditor.value?.textarea
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const text = createForm.value.content

  createForm.value.content = text.substring(0, start) + markdown + text.substring(end)

  setTimeout(() => {
    textarea.focus()
    const newPos = start + markdown.length
    textarea.setSelectionRange(newPos, newPos)
  }, 0)
}

const insertCodeBlock = () => {
  insertMarkdown('\n```\n// 在这里输入代码\n\n```\n')
}

// 编辑模式的Markdown插入功能
const editContentEditor = ref()

const insertMarkdownForEdit = (markdown) => {
  const textarea = editContentEditor.value?.textarea
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const text = editForm.value.content

  editForm.value.content = text.substring(0, start) + markdown + text.substring(end)

  setTimeout(() => {
    textarea.focus()
    const newPos = start + markdown.length
    textarea.setSelectionRange(newPos, newPos)
  }, 0)
}

const insertCodeBlockForEdit = () => {
  insertMarkdownForEdit('\n```\n// 在这里输入代码\n\n```\n')
}

// 格式化评论数显示
const formatCommentCount = (count) => {
  if (count < 1000) {
    return count.toString()
  } else if (count < 10000) {
    return (count / 1000).toFixed(1) + 'k'
  } else if (count < 1000000) {
    return Math.floor(count / 1000) + 'k'
  } else {
    return (count / 1000000).toFixed(1) + 'M'
  }
}

const createPost = async () => {
  createFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 确保有用户ID
        if (!currentUser.value?.id) {
          ElMessage.error('请先登录后再发布帖子')
          return
        }

        const postData = {
          title: createForm.value.title.trim(),
          content: createForm.value.content.trim(),
          userId: currentUser.value.id
        }

        // 验证数据
        if (!postData.title || !postData.content) {
          ElMessage.error('标题和内容不能为空')
          return
        }

        console.log('创建帖子数据:', postData)
        console.log('当前用户信息:', currentUser.value)
        const response = await createPostAPI(postData)
        console.log('创建帖子响应:', response)

        if (response.status) {
          ElMessage.success('帖子发布成功！')
          showCreateDialog.value = false
          createForm.value = { title: '', content: '' }
          activeTab.value = 'edit'

          // 重新加载帖子列表
          await loadPosts()
        } else {
          ElMessage.error(response.message || '发布帖子失败')
        }
      } catch (error) {
        console.error('发布帖子失败:', error)
        console.error('错误状态码:', error.response?.status)
        console.error('错误详情:', error.response?.data)
        console.error('请求配置:', error.config)

        let errorMessage = '发布帖子失败'
        if (error.response?.data?.message) {
          errorMessage += ': ' + error.response.data.message
        } else if (error.response?.status === 500) {
          errorMessage += ': 服务器内部错误，请检查数据格式或联系管理员'
        } else {
          errorMessage += ': ' + error.message
        }

        ElMessage.error(errorMessage)
      } finally {
        loading.value = false
      }
    }
  })
}

// 分页处理
const handleSizeChange = (val) => {
  console.log('页面大小改变:', val)
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
  loadPosts()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadPosts()
}

// 键盘快捷键处理
const handleKeydown = (event) => {
  if (event.ctrlKey && event.key === 'Enter' && showCreateDialog.value) {
    event.preventDefault()
    createPost()
  }
}

onMounted(async () => {
  document.addEventListener('keydown', handleKeydown)
  // 初始化加载用户信息和帖子数据
  await loadCurrentUser()
  loadPosts()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>




<template>
  <div class="forum-container">
    <!-- 讨论区头部 -->
    <el-card class="forum-header">
      <div class="header-content">
        <h2>
          <el-icon><ChatDotRound /></el-icon>
          讨论区
        </h2>
        <div class="header-center">
          <!-- 搜索区域 -->
          <div class="search-area">
            <el-input
                v-model="searchKeyword"
                placeholder="根据标题搜索帖子..."
                style="width: 420px"
                size="large"
                @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="handleSearch" size="large" style="margin-left: 10px;">
              搜索
            </el-button>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="loadPosts" :loading="loading" style="margin-right: 10px;">
            刷新
          </el-button>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><EditPen /></el-icon>
            发布帖子
          </el-button>
        </div>
      </div>
    </el-card>



    <!-- 帖子列表容器 -->
    <div class="post-list-container">
      <!-- 置顶帖子 -->
      <div v-if="pinnedPosts.length > 0" class="pinned-section">
        <div class="section-header">
          <el-icon class="pin-icon"><ArrowUp /></el-icon>
          <span class="section-title">置顶帖子</span>
        </div>
        <ul class="post-list">
          <li v-for="post in pinnedPosts" :key="post.id" class="post-item pinned">
            <!-- 用户头像 -->
            <a class="head-pic" :href="`/user/profile/${post.author.username}`">
              <img :src="post.author.avatar" :alt="post.author.username + '的头像'">
            </a>

            <!-- 帖子内容 -->
            <div class="discuss-detail">
              <div class="discuss-main">
                <a class="post-title" @click="viewPost(post.id)">
                  {{ post.title }}
                  <span class="disTop">置顶</span>
                </a>
              </div>

              <div class="feed-foot">
                <div class="feed-origin">
                  <p class="feed-tip">
                    <a class="d-name" :href="`/user/profile/${post.author.username}`">
                      {{ post.author.username }}
                    </a>
                    <span class="post-time">
                      <span>&nbsp;&nbsp;&nbsp;</span>{{ formatTime(post.publishTime) }}
                    </span>
                  </p>
                </div>

                <div class="feed-legend">
                  <div class="comment-count">
                    <el-icon class="comment-icon"><ChatDotRound /></el-icon>
                    <span class="count-number">{{ formatCommentCount(post.commentCount) }}</span>
                  </div>

                  <!-- 管理操作 - 始终占据空间以保持对齐 -->
                  <div class="post-actions">
                    <el-dropdown @command="handlePostAction" trigger="click" v-if="canManagePost(post)">
                      <el-button type="text" size="small" class="action-btn">
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="`edit-${post.id}`">编辑</el-dropdown-item>
                          <el-dropdown-item :command="`unpin-${post.id}`" v-if="canPinPost()">取消置顶</el-dropdown-item>
                          <el-dropdown-item :command="`delete-${post.id}`" style="color: #F56C6C">删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>

      <!-- 普通帖子列表 -->
      <div class="normal-posts-section" v-loading="loading">
        <ul class="post-list">
          <li v-for="post in filteredPosts" :key="post.id" class="post-item">
            <!-- 用户头像 -->
            <a class="head-pic" :href="`/user/profile/${post.author.username}`">
              <img :src="post.author.avatar" :alt="post.author.username + '的头像'">
            </a>

            <!-- 帖子内容 -->
            <div class="discuss-detail">
              <div class="discuss-main">
                <a class="post-title" @click="viewPost(post.id)">
                  {{ post.title }}
                </a>
              </div>

              <div class="feed-foot">
                <div class="feed-origin">
                  <p class="feed-tip">
                    <a class="d-name" :href="`/user/profile/${post.author.username}`">
                      {{ post.author.username }}
                    </a>
                    <span class="post-time">
                      <span>&nbsp;&nbsp;&nbsp;</span>{{ formatTime(post.publishTime) }}
                    </span>
                  </p>
                </div>

                <div class="feed-legend">
                  <div class="comment-count">
                    <el-icon class="comment-icon"><ChatDotRound /></el-icon>
                    <span class="count-number">{{ formatCommentCount(post.commentCount) }}</span>
                  </div>

                  <!-- 管理操作 - 始终占据空间以保持对齐 -->
                  <div class="post-actions">
                    <el-dropdown @command="handlePostAction" trigger="click" v-if="canManagePost(post)">
                      <el-button type="text" size="small" class="action-btn">
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="`edit-${post.id}`">编辑</el-dropdown-item>
                          <el-dropdown-item :command="`pin-${post.id}`" v-if="!post.isTop && canPinPost()">置顶</el-dropdown-item>
                          <el-dropdown-item :command="`unpin-${post.id}`" v-else-if="post.isTop && canPinPost()">取消置顶</el-dropdown-item>
                          <el-dropdown-item :command="`delete-${post.id}`" divided style="color: #F56C6C">删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[5, 10, 20, 50]"
              :total="totalPosts"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 发布帖子对话框 -->
    <el-dialog
        v-model="showCreateDialog"
        title="发布帖子"
        width="1200px"
        :close-on-click-modal="false"
        class="create-post-dialog"
    >
      <div class="create-post-container">
        <!-- 标题输入 -->
        <div class="post-basic-info">
          <el-form :model="createForm" :rules="createRules" ref="createFormRef">
            <el-form-item label="标题" prop="title">
              <el-input v-model="createForm.title" placeholder="请输入帖子标题" />
            </el-form-item>
          </el-form>
        </div>

        <!-- Markdown 编辑器 -->
        <div class="editor-container">
          <!-- 工具栏 -->
          <div class="editor-toolbar">
            <div class="toolbar-left">
              <el-button-group>
                <el-button
                    :type="activeTab === 'edit' ? 'primary' : ''"
                    size="small"
                    @click="activeTab = 'edit'"
                >
                  <el-icon><EditPen /></el-icon>
                  编辑
                </el-button>
                <el-button
                    :type="activeTab === 'preview' ? 'primary' : ''"
                    size="small"
                    @click="activeTab = 'preview'"
                >
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
                <el-button
                    :type="activeTab === 'split' ? 'primary' : ''"
                    size="small"
                    @click="activeTab = 'split'"
                >
                  <el-icon><Grid /></el-icon>
                  分屏
                </el-button>
              </el-button-group>
            </div>

            <div class="toolbar-right">
              <el-button size="small" @click="insertMarkdown('**粗体**')" title="粗体">
                <strong>B</strong>
              </el-button>
              <el-button size="small" @click="insertMarkdown('*斜体*')" title="斜体">
                <em>I</em>
              </el-button>
              <el-button size="small" @click="insertMarkdown('`代码`')" title="行内代码">
                Code
              </el-button>
              <el-button size="small" @click="insertCodeBlock" title="代码块">
                { }
              </el-button>
              <el-button size="small" @click="insertMarkdown('[链接文字](URL)')" title="链接">
                Link
              </el-button>
              <el-button size="small" @click="insertMarkdown('# 标题')" title="标题">
                H1
              </el-button>
            </div>
          </div>

          <!-- 内容区域 -->
          <div class="content-area" :class="activeTab">
            <!-- 编辑器面板 -->
            <div class="editor-panel" v-show="activeTab === 'edit' || activeTab === 'split'">
              <el-form-item prop="content" style="margin: 0;">
                <el-input
                    ref="contentEditor"
                    v-model="createForm.content"
                    type="textarea"
                    :rows="20"
                    placeholder="请输入帖子内容，支持 Markdown 格式..."
                    class="content-editor"
                />
              </el-form-item>
            </div>

            <!-- 预览面板 -->
            <div class="preview-panel" v-show="activeTab === 'preview' || activeTab === 'split'">
              <div class="preview-header">
                <h3>预览效果</h3>
                <span class="word-count">{{ wordCount }} 字</span>
              </div>
              <div class="preview-content">
                <div v-if="createForm.title" class="preview-title">
                  <h1>{{ createForm.title }}</h1>
                </div>
                <div v-if="createForm.content" class="preview-body" v-html="previewContent"></div>
                <div v-if="!createForm.content" class="empty-preview">
                  <el-empty description="开始编写内容，这里将显示预览效果" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="createPost" :disabled="!canPublish">
            <el-icon><Promotion /></el-icon>
            发布帖子
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑帖子对话框 -->
    <el-dialog
        v-model="showEditDialog"
        title="编辑帖子"
        width="1200px"
        :close-on-click-modal="false"
        class="create-post-dialog"
    >
      <div class="create-post-container">
        <!-- 标题输入 -->
        <div class="post-basic-info">
          <el-form :model="editForm" :rules="editRules" ref="editFormRef">
            <el-form-item label="标题" prop="title">
              <el-input v-model="editForm.title" placeholder="请输入帖子标题" />
            </el-form-item>
          </el-form>
        </div>

        <!-- Markdown 编辑器 -->
        <div class="editor-container">
          <!-- 工具栏 -->
          <div class="editor-toolbar">
            <div class="toolbar-left">
              <el-button-group>
                <el-button
                    :type="activeTab === 'edit' ? 'primary' : ''"
                    size="small"
                    @click="activeTab = 'edit'"
                >
                  <el-icon><EditPen /></el-icon>
                  编辑
                </el-button>
                <el-button
                    :type="activeTab === 'preview' ? 'primary' : ''"
                    size="small"
                    @click="activeTab = 'preview'"
                >
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
                <el-button
                    :type="activeTab === 'split' ? 'primary' : ''"
                    size="small"
                    @click="activeTab = 'split'"
                >
                  <el-icon><Grid /></el-icon>
                  分屏
                </el-button>
              </el-button-group>
            </div>

            <div class="toolbar-right">
              <el-button size="small" @click="insertMarkdownForEdit('**粗体**')" title="粗体">
                <strong>B</strong>
              </el-button>
              <el-button size="small" @click="insertMarkdownForEdit('*斜体*')" title="斜体">
                <em>I</em>
              </el-button>
              <el-button size="small" @click="insertMarkdownForEdit('`代码`')" title="行内代码">
                Code
              </el-button>
              <el-button size="small" @click="insertCodeBlockForEdit" title="代码块">
                { }
              </el-button>
              <el-button size="small" @click="insertMarkdownForEdit('[链接文字](URL)')" title="链接">
                Link
              </el-button>
              <el-button size="small" @click="insertMarkdownForEdit('# 标题')" title="标题">
                H1
              </el-button>
            </div>
          </div>

          <!-- 内容区域 -->
          <div class="content-area" :class="activeTab">
            <!-- 编辑器面板 -->
            <div class="editor-panel" v-show="activeTab === 'edit' || activeTab === 'split'">
              <el-form-item prop="content" style="margin: 0;">
                <el-input
                    ref="editContentEditor"
                    v-model="editForm.content"
                    type="textarea"
                    :rows="20"
                    placeholder="请输入帖子内容，支持 Markdown 格式..."
                    class="content-editor"
                />
              </el-form-item>
            </div>

            <!-- 预览面板 -->
            <div class="preview-panel" v-show="activeTab === 'preview' || activeTab === 'split'">
              <div class="preview-header">
                <h3>预览效果</h3>
                <span class="word-count">{{ editWordCount }} 字</span>
              </div>
              <div class="preview-content">
                <div v-if="editForm.title" class="preview-title">
                  <h1>{{ editForm.title }}</h1>
                </div>
                <div v-if="editForm.content" class="preview-body" v-html="editPreviewContent"></div>
                <div v-if="!editForm.content" class="empty-preview">
                  <el-empty description="开始编写内容，这里将显示预览效果" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="updatePost" :disabled="!canUpdate">
            <el-icon><Promotion /></el-icon>
            更新帖子
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>



<style lang="scss" scoped>
.forum-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  .forum-header {
    margin-bottom: 16px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        display: flex;
        align-items: center;
        margin: 0;

        .el-icon {
          margin-right: 10px;
          color: #409EFF;
        }
      }

      .header-center {
        flex: 1;
        display: flex;
        justify-content: center;
        margin: 0 20px;

        .search-area {
          display: flex;
          align-items: center;
        }
      }
    }
  }



  // 帖子列表容器样式
  .post-list-container {
    background-color: #ffffff;
    margin: 0;
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
  }

  // 置顶帖子区域
  .pinned-section {
    margin-bottom: 0;
    border-bottom: 1px solid #f0f0f0;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding: 16px 20px 8px;
      background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);

      .pin-icon {
        color: #ff9900;
        margin-right: 8px;
        font-size: 16px;
      }

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #ff9900;
      }
    }
  }

  // 普通帖子区域
  .normal-posts-section {
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  // 帖子列表通用样式
  .post-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .post-item {
    padding: 16px 20px;
    border-bottom: 1px solid #f5f5f5;
    display: flex;
    align-items: flex-start;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      background-color: #fafbfc;
    }

    &:last-child {
      border-bottom: none;
    }

    // 置顶帖子特殊样式
    &.pinned {
      background: linear-gradient(135deg, #fff9e6 0%, #fff2d9 100%);
      border-bottom: 1px solid #ffe7ba;
      margin: 0;
      padding: 16px 20px;

      &:hover {
        background: linear-gradient(135deg, #fff5d9 0%, #ffeccc 100%);
      }
    }
  }

  // 头像样式
  .head-pic {
    margin-right: 14px;
    flex-shrink: 0;

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #f0f0f0;
      transition: all 0.2s ease;

      &:hover {
        border-color: #409eff;
        transform: scale(1.05);
      }
    }
  }

  // 帖子详情区域
  .discuss-detail {
    flex: 1;
    min-width: 0; // 防止flex子元素溢出
  }

  .discuss-main {
    margin-bottom: 8px;
  }

  // 帖子标题样式
  .post-title {
    font-size: 16px;
    color: #1a1a1a;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    display: block;
    line-height: 1.4;
    transition: color 0.2s ease;

    &:hover {
      color: #409eff;
    }

    // 置顶标签
    .disTop {
      display: inline-block;
      padding: 2px 6px;
      margin-left: 8px;
      font-size: 10px;
      color: #ff6b35;
      background: #fff4e6;
      border: 1px solid #ff6b35;
      border-radius: 10px;
      vertical-align: middle;
      font-weight: 600;
    }
  }



  // 帖子底部信息
  .feed-foot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }

  .feed-origin {
    flex: 1;
  }

  .feed-tip {
    margin: 0;
    display: flex;
    align-items: center;
  }

  // 用户名样式
  .d-name {
    color: #409eff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
      color: #66b1ff;
    }
  }

  .post-time {
    color: #999;
    font-size: 12px;
  }

  // 统计信息样式
  .feed-legend {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;

    .comment-count {
      display: flex;
      align-items: center;
      background: #f8f9fa;
      padding: 4px 8px;
      border-radius: 12px;
      border: 1px solid #e9ecef;
      transition: all 0.2s ease;
      cursor: pointer;
      min-width: 60px; // 设置最小宽度确保一致性
      justify-content: center;

      &:hover {
        background: #e9ecef;
        border-color: #dee2e6;
        transform: translateY(-1px);
      }

      .comment-icon {
        color: #6c757d;
        font-size: 12px;
        margin-right: 4px;
        flex-shrink: 0; // 防止图标被压缩
      }

      .count-number {
        color: #495057;
        font-weight: 500;
        font-size: 12px;
        width: 32px; // 固定宽度
        text-align: center;
        white-space: nowrap; // 防止换行
        overflow: hidden;
        text-overflow: ellipsis; // 超长时显示省略号
      }
    }
  }

  // 操作按钮样式
  .post-actions {
    width: 32px; // 固定宽度确保对齐
    height: 32px; // 固定高度
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0; // 防止被压缩

    .action-btn {
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;
      color: #999;
      opacity: 0;

      &:hover {
        background-color: #f0f7ff;
        color: #409eff;
        opacity: 1;
      }
    }
  }

  .post-item:hover .post-actions .action-btn {
    opacity: 1;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .forum-container {
      padding: 0 8px;
    }

    .post-item {
      padding: 12px 16px;
    }

    .head-pic {
      margin-right: 12px;

      img {
        width: 36px;
        height: 36px;
      }
    }

    .post-title {
      font-size: 15px;
    }

    .feed-foot {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;
      margin-top: 6px;
    }

    .feed-legend {
      align-self: flex-end;
    }
  }

  @media (max-width: 480px) {
    .forum-container {
      padding: 0 4px;
    }

    .post-item {
      flex-direction: column;
      align-items: flex-start;
      padding: 10px 12px;

      .head-pic {
        margin-bottom: 8px;
        margin-right: 0;
      }

      .discuss-detail {
        width: 100%;
      }

      .post-actions {
        margin-left: 0;
        margin-top: 8px;
        opacity: 1;
      }
    }
  }
}

// 发布帖子对话框样式
:deep(.create-post-dialog) {
  .el-dialog__body {
    padding: 20px;
    padding-bottom: 10px; // 减少底部内边距
  }

  .el-dialog__footer {
    padding-top: 10px; // 减少顶部内边距
  }
}

.create-post-container {
  .post-basic-info {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #EBEEF5;

    .el-form-item {
      margin-bottom: 0;
    }
  }

  .editor-container {
    margin-bottom: 10px; // 减少编辑器容器的底部边距

    .editor-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      background: #F8F9FA;
      border: 1px solid #EBEEF5;
      border-bottom: none;
      border-radius: 8px 8px 0 0;

      .toolbar-left {
        .el-button-group {
          .el-button {
            border-radius: 4px;
            margin-right: 2px;

            &:first-child {
              border-radius: 4px 0 0 4px;
            }

            &:last-child {
              border-radius: 0 4px 4px 0;
            }
          }
        }
      }

      .toolbar-right {
        display: flex;
        gap: 5px;

        .el-button {
          padding: 5px 8px;
          font-size: 12px;

          strong, em {
            font-size: 14px;
          }
        }
      }
    }

    .content-area {
      border: 1px solid #EBEEF5;
      border-radius: 0 0 8px 8px;
      height: 400px; // 固定高度而不是最小高度
      display: flex;

      &.edit {
        .editor-panel {
          width: 100%;
        }
      }

      &.preview {
        .preview-panel {
          width: 100%;
        }
      }

      &.split {
        .editor-panel {
          width: 50%;
          border-right: 1px solid #EBEEF5;
        }

        .preview-panel {
          width: 50%;
        }
      }

      .editor-panel {
        display: flex;
        flex-direction: column;
        height: 100%; // 确保编辑器面板占满容器高度

        .el-form-item {
          flex: 1;
          margin: 0;
          height: 100%; // 确保表单项占满高度

          .content-editor {
            height: 100%;

            :deep(.el-textarea__inner) {
              height: 100% !important; // 让文本域占满可用高度
              resize: none;
              border: none;
              border-radius: 0;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 14px;
              line-height: 1.6;
              padding: 15px;
              box-sizing: border-box; // 确保padding不会影响高度计算
            }
          }
        }
      }

      .preview-panel {
        display: flex;
        flex-direction: column;
        background: #FAFBFC;
        height: 100%; // 确保预览面板占满容器高度

        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          border-bottom: 1px solid #EBEEF5;
          background: white;
          flex-shrink: 0; // 防止头部被压缩

          h3 {
            margin: 0;
            font-size: 16px;
            color: #303133;
          }

          .word-count {
            font-size: 12px;
            color: #909399;
          }
        }

        .preview-content {
          flex: 1;
          padding: 20px;
          overflow-y: auto; // 确保内容过多时显示滚动条
          background: white;
          min-height: 0; // 重要：允许flex子项缩小

          .preview-title {
            margin-bottom: 20px;

            h1 {
              margin: 0;
              font-size: 24px;
              color: #303133;
              line-height: 1.3;
            }
          }

          .preview-body {
            line-height: 1.8;
            color: #303133;

            :deep(h1) {
              font-size: 20px;
              margin: 20px 0 15px 0;
              color: #303133;
              border-bottom: 2px solid #409EFF;
              padding-bottom: 8px;
            }

            :deep(h2) {
              font-size: 18px;
              margin: 18px 0 12px 0;
              color: #409EFF;
            }

            :deep(h3) {
              font-size: 16px;
              margin: 15px 0 10px 0;
              color: #606266;
            }

            :deep(strong) {
              color: #E6A23C;
              font-weight: 600;
            }

            :deep(em) {
              color: #909399;
              font-style: italic;
            }

            :deep(code) {
              background: #f5f7fa;
              padding: 2px 6px;
              border-radius: 4px;
              font-family: 'Courier New', monospace;
              color: #E6A23C;
              font-size: 13px;
            }

            :deep(pre) {
              background: #f5f7fa;
              padding: 15px;
              border-radius: 8px;
              overflow-x: auto;
              margin: 15px 0;
              border-left: 4px solid #409EFF;

              code {
                background: none;
                padding: 0;
                color: #303133;
                font-size: 13px;
              }
            }

            :deep(a) {
              color: #409EFF;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }

          .empty-preview {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px; // 确保空状态有足够的高度
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
  }
}
</style>

