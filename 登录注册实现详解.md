# CodeDuel 登录注册系统实现详解

## 系统架构概述

CodeDuel 采用前后端分离架构，登录注册系统包含以下组件：

```
前端(Vue3) ←→ Java后端(Spring Boot) ←→ Python微服务 ←→ Codeforces API
     ↓              ↓                    ↓              ↓
   用户界面      业务逻辑处理          API代理服务      外部数据源
```

## 技术栈

- **前端**: Vue 3 + Element Plus + Pinia + Vue Router
- **后端**: Spring Boot + MyBatis Plus + MySQL
- **微服务**: Python Flask (Codeforces API代理)
- **安全**: SHA-256密码加密 + Session管理

## 核心功能实现

### 1. 用户登录功能

#### 前端实现 (`Login.vue`)

```javascript
// 登录函数
const login = () => {
  // 验证输入
  if (!usersname.value || !password.value) {
    ElMessage.error('请输入用户名和密码');
    return;
  }

  // 构建登录对象
  let obj = {
    codeforcesId: usersname.value,  // 使用Codeforces ID作为用户名
    password: password.value
  }

  // 调用登录API
  usersLogin(obj).then(res => {
    if (res.data == null) {
      ElMessage({
        message: '用户名密码错误',
        type: 'warning',
      })
    } else {
      // 检查用户状态（是否被封禁）
      if (res.data.status === 1) {
        const banReason = res.data.banReason || '违反平台规定'
        ElMessage({
          message: `账户已被封禁，原因：${banReason}`,
          type: 'error',
        })
        return
      }

      // 登录成功，保存用户信息到store
      userInfoStore.setUserInfo(res.data)
      ElMessage.success('登录成功')
      router.push('/dashboard/home')  // 跳转到主页
    }
  })
}
```

#### 后端登录接口 (`UsersController.java`)

```java
/**
 * 用户登录接口
 * 接收用户名和密码，验证后返回用户信息
 */
@RequestMapping(value = "/login", method = RequestMethod.POST)
@ResponseBody
public JsonResponse<Users> login(@RequestBody Users users) {
    try {
        // 1. 加密用户输入的密码（使用简单加密算法）
        if (users.getPassword() != null) {
            String encryptedPassword = PasswordUtils.simpleEncrypt(users.getPassword());
            users.setPassword(encryptedPassword);
        }

        // 2. 调用业务层进行登录验证
        Users users1 = usersService.login(users);
        
        if (users1 != null) {
            // 登录成功，清除密码字段后返回
            users1.setPassword(null);
            logger.info("用户 {} 登录成功", users1.getCodeforcesId());
        } else {
            logger.warn("用户 {} 登录失败", users.getCodeforcesId());
        }
        
        return JsonResponse.success(users1);
    } catch (Exception e) {
        logger.error("登录过程发生错误: {}", e.getMessage(), e);
        return JsonResponse.failure("登录失败，请稍后重试");
    }
}
```

#### 登录业务逻辑 (`UsersServiceImpl.java`)

```java
@Override
public Users login(Users users) {
    // 1. 构建查询条件（用户名+密码）
    LambdaQueryWrapper<Users> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(Users::getCodeforcesId, users.getCodeforcesId());
    queryWrapper.eq(Users::getPassword, users.getPassword());
    
    // 2. 查询数据库
    Users users1 = usersMapper.selectOne(queryWrapper);

    // 3. 如果登录成功，更新最后登录时间
    if (users1 != null) {
        users1.setLastLogin(java.time.LocalDateTime.now());
        this.updateById(users1);  // 更新数据库
    }

    // 4. 保存用户信息到Session
    SessionUtils.saveCurrentUserInfo(users1);

    return users1;
}
```

### 2. 用户注册功能

#### 注册流程设计

CodeDuel 采用独特的 **Codeforces身份验证注册** 机制：

1. **用户填写信息**: 输入Codeforces用户名和密码
2. **生成验证码**: 系统生成16位随机字符串
3. **身份验证**: 用户需要将验证码设置为Codeforces个人资料的firstName字段
4. **验证并注册**: 系统验证身份后自动完成注册

#### 前端注册流程 (`Login.vue`)

```javascript
// 注册函数
const register = async () => {
  // 1. 表单验证
  if (!usersname.value || !password.value || !confirmPassword.value) {
    ElMessage.error('请填写完整信息');
    return;
  }

  if (password.value !== confirmPassword.value) {
    ElMessage.error('两次密码输入不一致');
    return;
  }

  try {
    // 2. 生成验证码
    const response = await generateVerificationCode();
    
    if (response && response.status) {
      verificationCode.value = response.data;
      // 3. 显示验证对话框
      showVerificationDialog.value = true;
    } else {
      ElMessage.error('生成验证码失败，请重试');
    }
  } catch (error) {
    ElMessage.error('注册失败，请重试');
  }
}
```

#### 验证对话框组件 (`VerificationDialog.vue`)

```javascript
/**
 * 验证并注册用户（合并操作）
 * 这个方法将验证和注册合并为一个步骤：
 * 1. 验证用户是否已正确设置Codeforces的firstName字段
 * 2. 验证成功后直接调用后端的验证并注册接口
 * 3. 注册成功后显示成功页面并启动倒计时跳转
 */
const verifyAndRegister = async () => {
  isVerifying.value = true
  
  try {
    // 准备请求数据
    const registerData = {
      codeforcesId: props.codeforcesId,
      password: props.password,
      verificationString: verificationString.value
    }

    // 调用后端的合并接口（验证+注册）
    const response = await fetch('/api/users/verify-codeforces', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registerData)
    })

    const responseData = await response.json()

    if (responseData.status === true) {
      // 注册成功
      registrationSuccess.value = true
      startCountdown()  // 启动倒计时跳转
      ElMessage.success('🎉 注册成功！欢迎加入CodeDuel！')
    } else {
      ElMessage.error(`❌ ${responseData.message || '验证或注册失败'}`)
    }
  } catch (error) {
    ElMessage.error('❌ 网络错误，请重试')
  } finally {
    isVerifying.value = false
  }
}
```

#### 后端注册接口 (`UsersController.java`)

```java
/**
 * 验证Codeforces用户身份并完成注册
 * 这是一个合并接口，同时完成验证和注册两个步骤
 */
@PostMapping("/verify-codeforces")
@ResponseBody
public JsonResponse<Users> verifyCodeforcesUser(@RequestBody UserRegisterDTO registerDTO) {
    try {
        // 1. 参数验证
        String codeforcesId = registerDTO.getCodeforcesId().trim();
        String password = registerDTO.getPassword().trim();
        String verificationString = registerDTO.getVerificationString().trim();

        // 2. 检查用户是否已存在
        Users existingUser = usersService.getUserByUsername(codeforcesId);
        if (existingUser != null) {
            return JsonResponse.failure("该Codeforces用户已注册，请直接登录");
        }

        // 3. 获取Codeforces用户完整信息
        CodeforcesUserInfo codeforcesUserInfo = verificationService.getCodeforcesUserInfo(codeforcesId);
        if (codeforcesUserInfo == null) {
            return JsonResponse.failure("无法获取Codeforces用户信息，请检查用户名是否正确");
        }

        // 4. 验证身份（检查firstName是否匹配验证字符串）
        boolean isVerified = verificationService.verifyCodeforcesUser(codeforcesId, verificationString);
        if (!isVerified) {
            return JsonResponse.failure("身份验证失败，请确保已正确设置Codeforces个人资料");
        }

        // 5. 创建新用户
        Users newUser = new Users();
        newUser.setCodeforcesId(codeforcesId);
        
        // 6. 加密密码
        String encryptedPassword = PasswordUtils.simpleEncrypt(password);
        newUser.setPassword(encryptedPassword);
        
        // 7. 设置从Codeforces获取的信息
        newUser.setAvatar(codeforcesUserInfo.getAvatar() != null ? codeforcesUserInfo.getAvatar() : "");
        newUser.setRating(codeforcesUserInfo.getRating() != null ? codeforcesUserInfo.getRating() : 1500);
        
        // 8. 设置系统默认值
        newUser.setStatus(0);  // 正常状态
        newUser.setIsAdmin(0); // 非管理员
        newUser.setRegisterTime(java.time.LocalDateTime.now());
        newUser.setUpdateTime(java.time.LocalDateTime.now());

        // 9. 保存到数据库
        boolean saved = usersService.save(newUser);
        
        if (saved) {
            // 清除密码字段后返回
            newUser.setPassword(null);
            return JsonResponse.success(newUser);
        } else {
            return JsonResponse.failure("注册失败，数据库保存出错，请稍后重试");
        }
    } catch (Exception e) {
        logger.error("注册过程发生错误: {}", e.getMessage(), e);
        return JsonResponse.failure("注册失败，请稍后重试");
    }
}
```

### 3. 密码加密机制

#### 密码工具类 (`PasswordUtils.java`)

```java
/**
 * 密码加密工具类
 * 使用SHA-256算法进行密码加密
 */
public class PasswordUtils {
    private static final String ALGORITHM = "SHA-256";
    
    /**
     * 简单密码加密（用于当前系统）
     */
    public static String simpleEncrypt(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            byte[] hashBytes = md.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 生成随机盐值
     */
    public static String generateSalt() {
        SecureRandom random = new SecureRandom();
        StringBuilder salt = new StringBuilder(16);
        for (int i = 0; i < 16; i++) {
            salt.append(SALT_CHARS.charAt(random.nextInt(SALT_CHARS.length())));
        }
        return salt.toString();
    }
    
    /**
     * 带盐值的密码加密
     */
    public static String encryptPassword(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            String saltedPassword = password + salt;
            byte[] hashBytes = md.digest(saltedPassword.getBytes());
            return Base64.getEncoder().encodeToString(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
}
```

### 4. Codeforces身份验证服务

#### 验证服务接口 (`IVerificationService.java`)

```java
/**
 * 验证码服务接口
 * 用于生成和验证Codeforces身份验证码
 */
public interface IVerificationService {

    /**
     * 生成验证字符串
     * 生成一个复杂的随机字符串用于Codeforces身份验证
     */
    String generateVerificationCode();

    /**
     * 验证Codeforces用户身份
     * 通过调用Codeforces API验证用户是否已将firstName设置为验证字符串
     */
    boolean verifyCodeforcesUser(String codeforcesId, String verificationString);

    /**
     * 获取Codeforces用户的完整信息
     * 通过调用Python服务获取用户的完整信息，包括头像等
     */
    CodeforcesUserInfo getCodeforcesUserInfo(String codeforcesId);
}
```

#### 验证服务实现 (`VerificationServiceImpl.java`)

```java
@Service
public class VerificationServiceImpl implements IVerificationService {

    private static final String PYTHON_SERVICE_BASE = "http://localhost:5000/api/codeforces";
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final int VERIFICATION_CODE_LENGTH = 16;

    /**
     * 生成16位随机验证码
     */
    @Override
    public String generateVerificationCode() {
        StringBuilder sb = new StringBuilder(VERIFICATION_CODE_LENGTH);

        for (int i = 0; i < VERIFICATION_CODE_LENGTH; i++) {
            int randomIndex = secureRandom.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(randomIndex));
        }

        String verificationCode = sb.toString();
        logger.info("生成验证码: {}", verificationCode);
        return verificationCode;
    }

    /**
     * 验证Codeforces用户身份
     * 通过Python微服务调用Codeforces API
     */
    @Override
    public boolean verifyCodeforcesUser(String codeforcesId, String verificationString) {
        try {
            // 构建Python服务API请求URL
            String apiUrl = PYTHON_SERVICE_BASE + "/user/verify";

            // 构建请求体
            String requestBody = String.format(
                "{\"handle\":\"%s\",\"verificationString\":\"%s\"}",
                codeforcesId, verificationString
            );

            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(apiUrl))
                    .timeout(Duration.ofSeconds(30))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request,
                    HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                logger.error("Python服务请求失败: HTTP {}", response.statusCode());
                return false;
            }

            // 解析响应
            JsonNode jsonResponse = objectMapper.readTree(response.body());
            JsonNode data = jsonResponse.get("data");

            boolean isVerified = data.get("verified").asBoolean();
            String actualFirstName = data.get("actualFirstName").asText();

            logger.info("用户 {} 的firstName: {}", codeforcesId, actualFirstName);
            logger.info("验证字符串: {}", verificationString);

            return isVerified;

        } catch (Exception e) {
            logger.error("验证过程发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
```

### 5. Python微服务 (Codeforces API代理)

#### 服务概述 (`codeforces_api_service.py`)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Codeforces API服务

这是一个独立的Python微服务，专门负责与Codeforces官方API的交互。
设计目的：
1. 解决Java HTTP客户端访问Codeforces API不稳定的问题
2. 提供稳定可靠的网络请求处理
3. 将外部API调用从主业务系统中分离
4. 提供统一的Codeforces数据访问接口

技术栈：
- Flask: 轻量级Web框架，提供RESTful API
- requests: 强大的HTTP库，处理网络请求
- flask-cors: 处理跨域请求

服务架构：
前端 ←→ Java后端 ←→ Python服务 ←→ Codeforces API
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
import logging

app = Flask(__name__)
CORS(app)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.route('/api/codeforces/user/verify', methods=['POST'])
def verify_user():
    """
    验证Codeforces用户身份

    请求体：
    {
        "handle": "用户名",
        "verificationString": "验证字符串"
    }

    返回：
    {
        "success": true/false,
        "message": "消息",
        "data": {
            "verified": true/false,
            "actualFirstName": "实际的firstName",
            "userInfo": {...}
        }
    }
    """
    try:
        data = request.get_json()
        handle = data.get('handle')
        verification_string = data.get('verificationString')

        print(f"🔍 开始验证用户: {handle}")
        print(f"🎯 验证字符串: {verification_string}")

        # 调用Codeforces API获取用户信息
        user_info = get_codeforces_user_info(handle)

        if user_info:
            first_name = user_info.get('firstName', '')
            is_verified = first_name == verification_string

            print(f"📝 用户 {handle} 的firstName: '{first_name}'")
            print(f"🎯 验证结果: {'✅ 通过' if is_verified else '❌ 失败'}")

            return jsonify({
                'success': True,
                'message': '验证完成',
                'data': {
                    'verified': is_verified,
                    'actualFirstName': first_name,
                    'expectedVerificationString': verification_string,
                    'userInfo': {
                        'handle': user_info.get('handle'),
                        'rating': user_info.get('rating'),
                        'country': user_info.get('country'),
                        'avatar': user_info.get('avatar', ''),
                        'titlePhoto': user_info.get('titlePhoto', ''),
                        'organization': user_info.get('organization', ''),
                        'rank': user_info.get('rank', ''),
                        'maxRating': user_info.get('maxRating', 0)
                    }
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': f'无法获取用户 {handle} 的信息'
            }), 404

    except Exception as e:
        logger.error(f"验证过程发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器内部错误'
        }), 500

def get_codeforces_user_info(handle):
    """
    从Codeforces API获取用户信息
    """
    try:
        url = f"https://codeforces.com/api/user.info?handles={handle}"

        print(f"📡 请求Codeforces API: {url}")

        response = requests.get(url, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data.get('status') == 'OK' and data.get('result'):
            user_info = data['result'][0]
            print(f"✅ 成功获取用户 {handle} 的信息")
            return user_info
        else:
            print(f"❌ Codeforces API返回错误: {data}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ 解析响应失败: {str(e)}")
        return None

if __name__ == '__main__':
    print("🚀 启动Codeforces API服务...")
    print("📍 服务地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
```

### 6. 数据库设计

#### 用户表结构 (`users`)

```sql
CREATE TABLE users (
    id                   BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    password             VARCHAR(100) NOT NULL COMMENT '加密密码',
    codeforces_id        VARCHAR(50) NOT NULL COMMENT 'Codeforces_ID(唯一)',
    avatar               VARCHAR(200) NOT NULL COMMENT '头像路径',
    rating               INT NOT NULL DEFAULT 1500 COMMENT '当前Rating（默认1500）',
    status               SMALLINT NOT NULL DEFAULT 0 COMMENT '状态(0正常1封禁)',
    ban_reason           VARCHAR(200) COMMENT '封禁原因 (当status=1时有效)',
    register_time        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    update_time          DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    is_admin             SMALLINT NOT NULL DEFAULT 0 COMMENT '管理员标识',
    last_login           DATETIME COMMENT '最后登录时间（可为空）',
    PRIMARY KEY (id),
    UNIQUE KEY uk_codeforces_id (codeforces_id)
) COMMENT '用户表';
```

#### 用户实体类 (`Users.java`)

```java
@TableName("users")
@ApiModel(value = "Users对象", description = "用户")
public class Users implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "加密密码")
    @TableField("password")
    private String password;

    @ApiModelProperty(value = "Codeforces_ID(唯一)")
    @TableField("codeforces_id")
    private String codeforcesId;

    @ApiModelProperty(value = "头像路径")
    @TableField("avatar")
    private String avatar;

    @ApiModelProperty(value = "当前Rating（默认1500）")
    @TableField("rating")
    private Integer rating;

    @ApiModelProperty(value = "状态(0正常1封禁)")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "封禁原因 (当status=1时有效)")
    @TableField("ban_reason")
    private String banReason;

    @ApiModelProperty(value = "是否为管理员")
    @TableField("is_admin")
    private Integer isAdmin;

    @ApiModelProperty(value = "注册时间")
    @TableField("register_time")
    private LocalDateTime registerTime;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "最后登录时间")
    @TableField("last_login")
    private LocalDateTime lastLogin;

    // getter和setter方法省略...
}
```

### 7. 前端状态管理

#### 用户信息Store (`userInfo.js`)

```javascript
import {defineStore} from "pinia";
import {ref} from "vue";

/**
 * 用户信息状态管理
 * 使用Pinia进行全局状态管理，支持持久化存储
 */
export const useUserInfoStore = defineStore('userInfo',
    () => {
        // 定义用户信息响应式变量
        const userInfo = ref({})

        // 设置用户信息
        const setUserInfo = (newUserInfo) => {
            userInfo.value = newUserInfo
        }

        // 清理用户信息（退出登录时使用）
        const removeUserInfo = () => {
            userInfo.value = {}
        }

        return {
            userInfo,
            setUserInfo,
            removeUserInfo
        }
    },
    {
        persist: true  // 启用持久化存储，刷新页面后用户信息不丢失
    }
);
```

#### Session管理工具 (`SessionUtils.java`)

```java
/**
 * Session工具类
 * 用于管理用户登录状态
 */
public class SessionUtils {
    private static final String USERKEY = "sessionUser";

    /**
     * 获取当前Session
     */
    public static HttpSession session() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        return attr.getRequest().getSession(true);
    }

    /**
     * 获取当前登录用户信息
     */
    public static Users getCurrentUserInfo() {
        return (Users) session().getAttribute(USERKEY);
    }

    /**
     * 保存当前用户信息到Session
     */
    public static void saveCurrentUserInfo(Users user) {
        session().setAttribute(USERKEY, user);
    }
}
```

### 8. 路由配置与权限控制

#### 前端路由配置 (`router/index.js`)

```javascript
import { createRouter, createWebHistory } from "vue-router";
import Login from "@/views/Login.vue";
import Dashboard from "@/views/Dashboard.vue";
import Home from "@/views/dashboard/Home.vue";
// ... 其他组件导入

// 定义路由关系
const routes = [
  // 根路径重定向到dashboard/home
  { path: '/', redirect: '/dashboard/home' },

  // 登录页面（无需认证）
  { path: '/login', component: Login },

  // Dashboard相关路由（需要登录）
  {
    path: '/dashboard',
    component: Dashboard,
    children: [
      { path: 'home', component: Home },
      { path: 'forum', component: Forum },
      { path: 'battle', component: Battle },
      { path: 'ranking', component: Ranking },
      { path: 'profile/:username', component: Profile },
    ]
  },

  // Admin相关路由（需要管理员权限）
  {
    path: '/admin',
    component: Admin,
    children: [
      { path: 'userlist', component: UserList },
      { path: 'userinfo', component: UserInfo }
    ]
  }
]

// 创建路由器
const router = createRouter({
  history: createWebHistory(),
  routes: routes
})

export default router
```

### 9. API接口定义

#### 前端API封装 (`api/api.js`)

```javascript
import request from "@/utils/request";

// 用户认证相关API
export const usersLogin = users => request.post("/api/users/login", users);
export const getInfo = () => request.get("/api/auth/getUserInfo");
export const getUserByUsername = username => request.get(`/api/users/profile/${username}`);

// 用户注册相关API
export const userRegister = (data) => request.post("/api/users/register", data);
export const generateVerificationCode = () => request.get("/api/users/generate-verification-code");

// 用户管理相关API
export const updateUser = user => request.put("/api/users/update", user);
export const pageUsers = query => request.get("/api/users/pageList", { params: query });
export const getUserList = params => request.get("/api/users/list", { params });
```

### 10. 系统实现的功能特性

#### 10.1 安全特性

1. **密码加密**: 使用SHA-256算法对用户密码进行不可逆加密
2. **Session管理**: 基于Spring Session的用户状态管理
3. **身份验证**: 通过Codeforces官方API验证用户真实身份
4. **防重复注册**: 检查用户是否已存在，避免重复注册

#### 10.2 用户体验优化

1. **两步式注册**: 简化注册流程，用户体验友好
2. **实时验证**: 即时验证用户输入，提供及时反馈
3. **自动跳转**: 注册成功后自动跳转到登录页面
4. **状态持久化**: 用户登录状态在页面刷新后保持

#### 10.3 系统集成特性

1. **微服务架构**: Python服务专门处理外部API调用
2. **跨域支持**: 前后端分离架构，支持跨域请求
3. **数据同步**: 自动同步Codeforces用户信息（头像、Rating等）
4. **扩展性**: 模块化设计，便于功能扩展

### 11. 部署配置

#### 11.1 后端配置 (`application.yml`)

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************
    username: root
    password: 123456

server:
  port: 8080
  servlet:
    encoding:
      charset: utf-8

mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml

logging:
  level:
    com.xju.codeduel.mapper: debug
```

#### 11.2 前端代理配置 (`vite.config.js`)

```javascript
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0',
    port: 5174,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8080',
        changeOrigin: true,
        secure: false,
        rewrite: path => path.replace(/^\/api/, '/api')
      }
    }
  }
})
```

## 总结

CodeDuel的登录注册系统是一个完整的、安全的、用户友好的身份认证解决方案。它不仅实现了基本的用户认证功能，还通过与Codeforces平台的集成，确保了用户身份的真实性。系统采用现代化的技术栈和架构设计，具有良好的可维护性和扩展性。

**主要创新点**：
1. **Codeforces身份验证机制**: 通过修改Codeforces个人资料进行身份验证
2. **微服务架构**: 使用Python服务作为API代理，提高系统稳定性
3. **前后端分离**: 现代化的开发架构，便于团队协作
4. **用户体验优化**: 简化的注册流程和友好的用户界面
