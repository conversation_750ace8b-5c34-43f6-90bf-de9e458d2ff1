<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4bfcbd0e-48b1-433f-a8a9-1e426dce0236" name="更改" comment="删除测试注册" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Vue Composition API Component" />
      </list>
    </option>
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="origin/master" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\maven\maven_rs" />
        <option name="userSettingsFile" value="D:\maven\settings-ali.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="PerforceDirect.Settings">
    <option name="CHARSET" value="无" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="302ISMd9fcpyUrwjjvYiWizMgDg" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;zhangrun&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/22-9/大三/大三下/小学期实训/CodeDuel/code-fuel-frontend/src/components&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2024.1.4\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;vue.recent.templates&quot;: [
      &quot;Vue Composition API Component&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-frontend\src\components" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-frontend\src\api" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-frontend\src\views" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-frontend\src\views\dashboard" />
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-frontend\src\views\user" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="F:\22-9\大三\大三下\小学期实训\CodeDuel\code-fuel-frontend\src\views\dashboard" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4bfcbd0e-48b1-433f-a8a9-1e426dce0236" name="更改" comment="" />
      <created>1752131169373</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752131169373</updated>
      <workItem from="1752819241288" duration="12335000" />
      <workItem from="1752886988276" duration="16763000" />
      <workItem from="1753232668726" duration="2254000" />
      <workItem from="1753340613264" duration="3984000" />
      <workItem from="1753351377349" duration="10475000" />
    </task>
    <task id="LOCAL-00001" summary="登陆界面修改">
      <option name="closed" value="true" />
      <created>1752821489061</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752821489061</updated>
    </task>
    <task id="LOCAL-00002" summary="登陆界面修改">
      <option name="closed" value="true" />
      <created>1752822679768</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752822679768</updated>
    </task>
    <task id="LOCAL-00003" summary="文字内容修改">
      <option name="closed" value="true" />
      <created>1752823518958</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752823518958</updated>
    </task>
    <task id="LOCAL-00004" summary="增加重定向">
      <option name="closed" value="true" />
      <created>1752825724956</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752825724956</updated>
    </task>
    <task id="LOCAL-00005" summary="更改登录成功的跳转位置">
      <option name="closed" value="true" />
      <created>1752825774880</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752825774880</updated>
    </task>
    <task id="LOCAL-00006" summary="更改导航栏中排行榜的路径">
      <option name="closed" value="true" />
      <created>1752828972998</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752828972998</updated>
    </task>
    <task id="LOCAL-00007" summary="添加排行榜路由">
      <option name="closed" value="true" />
      <created>1752828995605</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752828995605</updated>
    </task>
    <task id="LOCAL-00008" summary="排行榜前端文件">
      <option name="closed" value="true" />
      <created>1752829047245</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752829047245</updated>
    </task>
    <task id="LOCAL-00009" summary="个人中心">
      <option name="closed" value="true" />
      <created>1752906879841</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752906879841</updated>
    </task>
    <task id="LOCAL-00010" summary="注册弹窗">
      <option name="closed" value="true" />
      <created>1752999340090</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752999340090</updated>
    </task>
    <task id="LOCAL-00011" summary="修正登陆界面">
      <option name="closed" value="true" />
      <created>1752999662341</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752999662341</updated>
    </task>
    <task id="LOCAL-00012" summary="首页展示函数">
      <option name="closed" value="true" />
      <created>1753002468657</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753002468657</updated>
    </task>
    <task id="LOCAL-00013" summary="更改首页布局">
      <option name="closed" value="true" />
      <created>1753002511744</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753002511744</updated>
    </task>
    <task id="LOCAL-00014" summary="xiu">
      <option name="closed" value="true" />
      <created>1753011942892</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753011942892</updated>
    </task>
    <task id="LOCAL-00015" summary="实现注册">
      <option name="closed" value="true" />
      <created>1753011979041</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753011979041</updated>
    </task>
    <task id="LOCAL-00016" summary="分装注册相关api请求">
      <option name="closed" value="true" />
      <created>1753012025248</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753012025248</updated>
    </task>
    <task id="LOCAL-00017" summary="注册功能">
      <option name="closed" value="true" />
      <created>1753012492558</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753012492558</updated>
    </task>
    <task id="LOCAL-00018" summary="封装首页获取数据请求">
      <option name="closed" value="true" />
      <created>1753014233164</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753014233164</updated>
    </task>
    <task id="LOCAL-00019" summary="系统主页完善修改">
      <option name="closed" value="true" />
      <created>1753346491297</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753346491297</updated>
    </task>
    <task id="LOCAL-00020" summary="排行榜界面优化">
      <option name="closed" value="true" />
      <created>1753348234993</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753348234993</updated>
    </task>
    <task id="LOCAL-00021" summary="删除测试注册">
      <option name="closed" value="true" />
      <created>1753352550676</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753352550676</updated>
    </task>
    <option name="localTasksCounter" value="22" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/zhangrun" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="登陆界面修改" />
    <MESSAGE value="文字内容修改" />
    <MESSAGE value="增加重定向" />
    <MESSAGE value="更改登录成功的跳转位置" />
    <MESSAGE value="更改导航栏中排行榜的路径" />
    <MESSAGE value="添加排行榜路由" />
    <MESSAGE value="排行榜前端文件" />
    <MESSAGE value="个人中心" />
    <MESSAGE value="修正登陆界面" />
    <MESSAGE value="首页展示函数" />
    <MESSAGE value="更改首页布局" />
    <MESSAGE value="注册弹窗" />
    <MESSAGE value="实现注册" />
    <MESSAGE value="分装注册相关api请求" />
    <MESSAGE value="注册功能" />
    <MESSAGE value="封装首页获取数据请求" />
    <MESSAGE value="系统主页完善修改" />
    <MESSAGE value="排行榜界面优化" />
    <MESSAGE value="删除测试注册" />
    <option name="LAST_COMMIT_MESSAGE" value="删除测试注册" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/views/dashboard/Home.vue</url>
          <line>16</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>