package com.xju.codeduel.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("users")
@ApiModel(value="Users对象", description="用户")
public class Users implements Serializable {

    private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

        @ApiModelProperty(value = "加密密码")
    @TableField("password")
    private String password;

        @ApiModelProperty(value = "Codeforces_ID(唯一)")
    @TableField("codeforces_id")
    private String codeforcesId;

        @ApiModelProperty(value = "头像路径")
    @TableField("avatar")
    private String avatar;

        @ApiModelProperty(value = "当前Rating（默认1500）")
    @TableField("rating")
    private Integer rating;

        @ApiModelProperty(value = "状态(0正常1封禁)")
    @TableField("status")
    private Integer status;

        @ApiModelProperty(value = "封禁原因 (当status=1时有效)")
    @TableField("ban_reason")
    private String banReason;

        @ApiModelProperty(value = "注册时间")
    @TableField("register_time")
    private LocalDateTime registerTime;

        @ApiModelProperty(value = "最后修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

        @ApiModelProperty(value = "管理员标识")
    @TableField("is_admin")
    private Integer isAdmin;

        @ApiModelProperty(value = "最后登录时间（可为空）")
    @TableField("last_login")
    private LocalDateTime lastLogin;


}
