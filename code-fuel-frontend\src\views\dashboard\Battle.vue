<template>
  <div class="battle-container">
    <!-- 对战模式选择 -->
    <el-card class="mode-selection" header="选择对战模式" v-if="!inBattle">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="mode-card" shadow="hover" @click="startRandomMatch">
            <div class="mode-content">
              <el-icon class="mode-icon"><Timer /></el-icon>
              <h3>随机匹配</h3>
              <p>系统自动为您匹配实力相当的对手</p>
              <el-button type="primary" size="large">开始匹配</el-button>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="mode-card" shadow="hover" @click="showRoomDialog = true">
            <div class="mode-content">
              <el-icon class="mode-icon"><House /></el-icon>
              <h3>房间对战</h3>
              <p>创建房间或加入好友的房间进行对战</p>
              <el-button type="success" size="large">房间对战</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 房间对话框 -->
    <el-dialog v-model="showRoomDialog" title="房间对战" width="500px">
      <el-tabs v-model="roomTabActive">
        <el-tab-pane label="创建房间" name="create">
          <el-form :model="createRoomForm" label-width="80px">
            <el-form-item label="房间名称">
              <el-input v-model="createRoomForm.roomName" placeholder="请输入房间名称" />
            </el-form-item>
            <el-form-item label="难度选择">
              <el-select v-model="createRoomForm.difficulty" placeholder="选择题目难度">
                <el-option label="简单" value="easy" />
                <el-option label="中等" value="medium" />
                <el-option label="困难" value="hard" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="createRoom">创建房间</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="加入房间" name="join">
          <el-form :model="joinRoomForm" label-width="80px">
            <el-form-item label="房间码">
              <el-input v-model="joinRoomForm.roomCode" placeholder="请输入房间码" />
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="joinRoom">加入房间</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 匹配中状态 -->
    <el-card class="matching-card" v-if="isMatching">
      <div class="matching-content">
        <el-icon class="matching-icon rotating"><Loading /></el-icon>
        <h3>正在匹配对手...</h3>
        <p>预计等待时间：{{ matchingTime }}秒</p>
        <el-button @click="cancelMatch">取消匹配</el-button>
      </div>
    </el-card>

    <!-- 对战界面 -->
    <div class="battle-arena" v-if="inBattle">
      <!-- 对战信息栏 -->
      <el-card class="battle-info">
        <el-row>
          <el-col :span="8">
            <div class="player-info">
              <el-avatar :src="currentUser.avatar" />
              <div class="player-details">
                <div class="player-name">{{ currentUser.codeforcesId }}</div>
                <div class="player-rating">Rating: {{ currentUser.rating }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8" class="battle-center">
            <div class="battle-status">
              <div class="timer">{{ formatTime(battleTime) }}</div>
              <div class="problem-title">{{ currentProblem.title }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="player-info opponent">
              <div class="player-details">
                <div class="player-name">{{ opponent.codeforcesId }}</div>
                <div class="player-rating">Rating: {{ opponent.rating }}</div>
              </div>
              <el-avatar :src="opponent.avatar" />
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 题目和代码编辑区 -->
      <el-row :gutter="20" class="battle-main">
        <el-col :span="12">
          <!-- 题目描述 -->
          <el-card class="problem-card" header="题目描述">
            <div class="problem-content">
              <h4>{{ currentProblem.title }}</h4>
              <div class="problem-difficulty">
                <el-tag :type="getDifficultyType(currentProblem.difficulty)">
                  难度: {{ currentProblem.difficulty }}
                </el-tag>
              </div>
              <div class="problem-description" v-html="currentProblem.description"></div>
              
              <div class="problem-examples">
                <h5>示例：</h5>
                <div v-for="(example, index) in currentProblem.examples" :key="index" class="example">
                  <div class="example-input">
                    <strong>输入：</strong>
                    <pre>{{ example.input }}</pre>
                  </div>
                  <div class="example-output">
                    <strong>输出：</strong>
                    <pre>{{ example.output }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <!-- 代码编辑器 -->
          <el-card class="code-card" header="代码编辑">
            <div class="code-editor">
              <div class="editor-toolbar">
                <el-select v-model="selectedLanguage" placeholder="选择语言">
                  <el-option label="C++" value="cpp" />
                  <el-option label="Java" value="java" />
                  <el-option label="Python" value="python" />
                </el-select>
                <el-button type="primary" @click="runCode">运行</el-button>
                <el-button type="success" @click="submitCode">提交</el-button>
              </div>
              <el-input
                v-model="userCode"
                type="textarea"
                :rows="20"
                placeholder="在这里编写您的代码..."
                class="code-textarea"
              />
            </div>
          </el-card>
          
          <!-- 运行结果 -->
          <el-card class="result-card" header="运行结果" v-if="runResult">
            <div class="result-content">
              <div class="result-status" :class="runResult.status">
                {{ runResult.status === 'success' ? '通过' : '错误' }}
              </div>
              <div class="result-details" v-if="runResult.details">
                {{ runResult.details }}
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 对战结果 -->
    <el-dialog v-model="showResultDialog" title="对战结果" width="400px" :close-on-click-modal="false">
      <div class="battle-result">
        <div class="result-icon">
          <el-icon v-if="battleResult.isWin" class="win-icon"><Trophy /></el-icon>
          <el-icon v-else class="lose-icon"><Close /></el-icon>
        </div>
        <h3>{{ battleResult.isWin ? '恭喜获胜！' : '很遗憾失败了' }}</h3>
        <div class="rating-change">
          <span>Rating变化：</span>
          <span :class="battleResult.ratingChange > 0 ? 'rating-up' : 'rating-down'">
            {{ battleResult.ratingChange > 0 ? '+' : '' }}{{ battleResult.ratingChange }}
          </span>
        </div>
        <div class="battle-stats">
          <p>用时：{{ formatTime(battleResult.duration) }}</p>
          <p>提交次数：{{ battleResult.submissions }}</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="backToSelection">返回选择</el-button>
        <el-button type="primary" @click="startNewBattle">再来一局</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import {
  Timer,
  House,
  Loading,
  Trophy,
  Close
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 状态管理
const inBattle = ref(false)
const isMatching = ref(false)
const matchingTime = ref(0)
const battleTime = ref(0)
const showRoomDialog = ref(false)
const showResultDialog = ref(false)
const roomTabActive = ref('create')

// 表单数据
const createRoomForm = ref({
  roomName: '',
  difficulty: ''
})

const joinRoomForm = ref({
  roomCode: ''
})

// 对战数据
const currentUser = ref({
  codeforcesId: 'user123',
  avatar: '',
  rating: 1500
})

const opponent = ref({
  codeforcesId: 'opponent456',
  avatar: '',
  rating: 1520
})

const currentProblem = ref({
  title: 'Two Sum',
  difficulty: 'Easy',
  description: '给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出和为目标值的那两个整数，并返回它们的数组下标。',
  examples: [
    {
      input: 'nums = [2,7,11,15], target = 9',
      output: '[0,1]'
    }
  ]
})

const selectedLanguage = ref('cpp')
const userCode = ref('')
const runResult = ref(null)
const battleResult = ref({
  isWin: true,
  ratingChange: 25,
  duration: 300,
  submissions: 2
})

// 定时器
let matchingTimer = null
let battleTimer = null

// 方法
const startRandomMatch = () => {
  isMatching.value = true
  matchingTime.value = 0
  
  matchingTimer = setInterval(() => {
    matchingTime.value++
    if (matchingTime.value >= 10) { // 模拟10秒后匹配成功
      clearInterval(matchingTimer)
      isMatching.value = false
      inBattle.value = true
      startBattleTimer()
      ElMessage.success('匹配成功！开始对战')
    }
  }, 1000)
}

const cancelMatch = () => {
  clearInterval(matchingTimer)
  isMatching.value = false
  matchingTime.value = 0
}

const createRoom = () => {
  // 创建房间逻辑
  ElMessage.success('房间创建成功！房间码：123456')
  showRoomDialog.value = false
}

const joinRoom = () => {
  // 加入房间逻辑
  ElMessage.success('成功加入房间！')
  showRoomDialog.value = false
  inBattle.value = true
  startBattleTimer()
}

const startBattleTimer = () => {
  battleTime.value = 0
  battleTimer = setInterval(() => {
    battleTime.value++
  }, 1000)
}

const runCode = () => {
  // 运行代码逻辑
  runResult.value = {
    status: 'success',
    details: '测试用例通过'
  }
}

const submitCode = () => {
  // 提交代码逻辑
  clearInterval(battleTimer)
  showResultDialog.value = true
}

const getDifficultyType = (difficulty) => {
  const types = {
    'Easy': 'success',
    'Medium': 'warning',
    'Hard': 'danger'
  }
  return types[difficulty] || 'info'
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const backToSelection = () => {
  showResultDialog.value = false
  inBattle.value = false
  battleTime.value = 0
  userCode.value = ''
  runResult.value = null
}

const startNewBattle = () => {
  showResultDialog.value = false
  startRandomMatch()
}

onUnmounted(() => {
  if (matchingTimer) clearInterval(matchingTimer)
  if (battleTimer) clearInterval(battleTimer)
})
</script>

<style lang="scss" scoped>
.battle-container {
  max-width: 1400px;
  margin: 0 auto;

  .mode-selection {
    .mode-card {
      cursor: pointer;
      transition: transform 0.3s;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      .mode-content {
        text-align: center;
        padding: 20px;
        
        .mode-icon {
          font-size: 3em;
          color: #409EFF;
          margin-bottom: 15px;
        }
        
        h3 {
          margin-bottom: 10px;
        }
        
        p {
          color: #666;
          margin-bottom: 20px;
        }
      }
    }
  }

  .matching-card {
    text-align: center;
    
    .matching-content {
      padding: 40px;
      
      .matching-icon {
        font-size: 3em;
        color: #409EFF;
        margin-bottom: 20px;
        
        &.rotating {
          animation: rotate 2s linear infinite;
        }
      }
      
      h3 {
        margin-bottom: 10px;
      }
      
      p {
        color: #666;
        margin-bottom: 20px;
      }
    }
  }

  .battle-arena {
    .battle-info {
      margin-bottom: 20px;
      
      .player-info {
        display: flex;
        align-items: center;
        
        &.opponent {
          justify-content: flex-end;
          
          .player-details {
            text-align: right;
            margin-right: 10px;
          }
        }
        
        .player-details {
          margin-left: 10px;
          
          .player-name {
            font-weight: bold;
          }
          
          .player-rating {
            color: #666;
            font-size: 0.9em;
          }
        }
      }
      
      .battle-center {
        text-align: center;
        
        .timer {
          font-size: 1.5em;
          font-weight: bold;
          color: #409EFF;
        }
        
        .problem-title {
          margin-top: 5px;
          color: #666;
        }
      }
    }
    
    .battle-main {
      .problem-card {
        height: 600px;
        overflow-y: auto;
        
        .problem-content {
          .problem-difficulty {
            margin: 10px 0;
          }
          
          .problem-description {
            margin: 15px 0;
            line-height: 1.6;
          }
          
          .problem-examples {
            .example {
              margin: 10px 0;
              padding: 10px;
              background-color: #f5f5f5;
              border-radius: 4px;
              
              .example-input,
              .example-output {
                margin: 5px 0;
                
                pre {
                  margin: 5px 0;
                  padding: 5px;
                  background-color: #fff;
                  border: 1px solid #ddd;
                  border-radius: 3px;
                }
              }
            }
          }
        }
      }
      
      .code-card {
        .editor-toolbar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          
          .el-select {
            width: 120px;
          }
        }
        
        .code-textarea {
          font-family: 'Courier New', monospace;
        }
      }
      
      .result-card {
        margin-top: 20px;
        
        .result-content {
          .result-status {
            font-weight: bold;
            margin-bottom: 10px;
            
            &.success {
              color: #67C23A;
            }
            
            &.error {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }

  .battle-result {
    text-align: center;
    
    .result-icon {
      font-size: 4em;
      margin-bottom: 20px;
      
      .win-icon {
        color: #67C23A;
      }
      
      .lose-icon {
        color: #F56C6C;
      }
    }
    
    .rating-change {
      margin: 15px 0;
      font-size: 1.2em;
      
      .rating-up {
        color: #67C23A;
        font-weight: bold;
      }
      
      .rating-down {
        color: #F56C6C;
        font-weight: bold;
      }
    }
    
    .battle-stats {
      margin-top: 20px;
      color: #666;
      
      p {
        margin: 5px 0;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
