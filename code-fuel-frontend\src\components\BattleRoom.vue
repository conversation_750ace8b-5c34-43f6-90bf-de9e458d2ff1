<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import SockJS from 'sockjs-client'
import { Stomp } from '@stomp/stompjs'
import {
  Link,
  Trophy,
  Clock,
  Close,
  Check,
  Refresh,
  Flag
} from '@element-plus/icons-vue'
import { checkSubmissionStatus, finishBattle, getRoomInfo, leaveRoom } from '@/api/api'
import { useUserInfoStore } from '@/stores/userInfo'

// ==================== 组件属性和事件 ====================

/**
 * 组件属性定义
 * - roomInfo: 房间信息对象
 * - visible: 是否显示对战界面
 */
const props = defineProps({
  roomInfo: {
    type: Object,
    required: false,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

/**
 * 组件事件定义
 * - close: 关闭对战界面
 * - battle-end: 对战结束
 */
const emit = defineEmits(['close', 'battle-end'])

// ==================== 响应式数据 ====================

const userInfoStore = useUserInfoStore()

// 对战状态
const battleStatus = ref('ONGOING') // ONGOING, FINISHED
const battleTime = ref(0) // 对战时间（秒）
const battleTimer = ref(null)

// WebSocket连接
let stompClient = null

// 房间状态检查定时器
let roomStatusCheckTimer = null

// 提交状态
const submissionStatus = ref({
  checking: false,
  lastCheck: null,
  isPassed: false
})

// 对手提交状态
const opponentStatus = ref({
  checking: false,
  lastCheck: null,
  isPassed: false
})

// 当前用户信息
const currentUser = computed(() => userInfoStore.userInfo)

// 对手信息
const opponent = computed(() => {
  if (!props.roomInfo || !props.roomInfo.participants) return null
  
  return props.roomInfo.participants.find(p => p.id !== currentUser.value.id)
})

// 题目信息
const problem = computed(() => props.roomInfo?.problem)

// Codeforces题目链接
const problemUrl = computed(() => {
  if (!problem.value) return ''

  // 使用contestId和problemId生成链接
  // 格式：https://codeforces.com/problemset/problem/contestId/problemId
  const contestId = problem.value.contestId
  const problemId = problem.value.problemId

  if (contestId && problemId) {
    return `https://codeforces.com/problemset/problem/${contestId}/${problemId}`
  }

  console.warn('题目信息不完整:', problem.value)
  return ''
})

// 格式化时间显示
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// ==================== 核心业务方法 ====================

/**
 * 开始对战计时器
 */
function startBattleTimer() {
  if (battleTimer.value) {
    clearInterval(battleTimer.value)
  }

  battleTimer.value = setInterval(() => {
    battleTime.value++
  }, 1000)
}

/**
 * 停止对战计时器
 */
function stopBattleTimer() {
  console.log('🛑 停止对战计时器')
  if (battleTimer.value) {
    clearInterval(battleTimer.value)
    battleTimer.value = null
  }
}

/**
 * 开始房间状态检查
 */
function startRoomStatusCheck() {
  // 每3秒检查一次房间状态
  roomStatusCheckTimer = setInterval(async () => {
    if (battleStatus.value === 'FINISHED') {
      return // 对战已结束，不需要检查
    }

    try {
      const response = await getRoomInfo(props.roomInfo.roomCode)

      if (!response.status || !response.data) {
        // 房间不存在或已解散
        console.log('🏠 房间已解散')
        handleRoomDissolved()
        return
      }

      const roomInfo = response.data

      // 检查房间状态
      if (roomInfo.status === 'FINISHED') {
        console.log('🏁 检测到对战已结束')
        handleBattleFinishedByRoom(roomInfo)
      }

    } catch (error) {
      console.error('❌ 检查房间状态失败:', error)
    }
  }, 3000)
}

/**
 * 停止房间状态检查
 */
function stopRoomStatusCheck() {
  if (roomStatusCheckTimer) {
    clearInterval(roomStatusCheckTimer)
    roomStatusCheckTimer = null
  }
}

/**
 * 处理房间解散
 */
function handleRoomDissolved() {
  stopBattleTimer()
  stopRoomStatusCheck()
  battleStatus.value = 'FINISHED'

  ElMessage.warning('房间已解散，对战结束')
  emit('close')
}

/**
 * 处理房间状态显示对战已结束
 */
function handleBattleFinishedByRoom(roomInfo) {
  if (battleStatus.value === 'FINISHED') {
    return // 已经处理过了
  }

  stopBattleTimer()
  stopRoomStatusCheck()
  battleStatus.value = 'FINISHED'

  // 从房间信息中获取对战结果
  const battleResult = roomInfo.battleResult || {}
  const winnerId = battleResult.winnerId
  const reason = battleResult.reason || 'UNKNOWN'

  const currentUserId = currentUser.value.id
  const isWinner = winnerId === currentUserId

  let resultMessage = ''
  let messageType = 'info'

  if (isWinner) {
    switch (reason) {
      case 'SOLVED':
        resultMessage = '🎉 恭喜！您成功解决了题目，获得胜利！'
        messageType = 'success'
        break
      case 'SURRENDER':
      case 'QUIT':
        resultMessage = '🎉 对手放弃了对战，您获得胜利！'
        messageType = 'success'
        break
      default:
        resultMessage = '🎉 恭喜您获得胜利！'
        messageType = 'success'
    }
  } else {
    switch (reason) {
      case 'SOLVED':
        resultMessage = '😔 对手率先解决了题目，您败北了！'
        messageType = 'warning'
        break
      case 'SURRENDER':
        resultMessage = '😔 您主动放弃了对战，判定为失败！'
        messageType = 'warning'
        break
      case 'QUIT':
        resultMessage = '😔 您退出了对战，判定为失败！'
        messageType = 'warning'
        break
      default:
        resultMessage = '😔 很遗憾，您败北了！'
        messageType = 'warning'
    }
  }

  ElMessage({
    message: resultMessage,
    type: messageType,
    duration: 8000,
    showClose: true
  })

  // 通知父组件对战结束
  emit('battle-end', {
    winner: isWinner ? currentUser.value : opponent.value,
    loser: isWinner ? opponent.value : currentUser.value,
    battleTime: battleTime.value,
    roomInfo: props.roomInfo,
    reason: reason
  })
}

/**
 * 检查提交状态
 *
 * 功能说明：
 * 1. 调用后端API检查用户在Codeforces上的提交状态
 * 2. 如果通过则标记为胜利
 * 3. 更新界面显示
 *
 * 注意：Python服务期望的problemId格式是 "contestId-problemIndex"
 */
const checkSubmission = async () => {
  if (!currentUser.value || !problem.value) {
    ElMessage.error('用户信息或题目信息不完整')
    return
  }

  // 检查题目信息是否完整
  if (!problem.value.contestId || !problem.value.problemId) {
    ElMessage.error('题目信息不完整，无法检查提交状态')
    return
  }

  submissionStatus.value.checking = true

  try {
    // 构造Python服务期望的题目ID格式：contestId-problemIndex
    const fullProblemId = `${problem.value.contestId}-${problem.value.problemId}`

    const response = await checkSubmissionStatus({
      codeforcesHandle: currentUser.value.codeforcesId,
      problemId: fullProblemId
    })
    
    if (response.status) {
      const isPassed = response.data.isPassed
      submissionStatus.value.isPassed = isPassed
      submissionStatus.value.lastCheck = new Date()
      
      if (isPassed) {
        ElMessage.success('恭喜！您已通过该题目！')
        await handleBattleWin()
      } else {
        ElMessage.info('暂未通过，请继续努力')
      }
    } else {
      ElMessage.error(response.message || '检查提交状态失败')
    }
    
  } catch (error) {
    console.error('❌ 检查提交状态失败:', error)
    ElMessage.error('检查提交状态失败: ' + error.message)
  } finally {
    submissionStatus.value.checking = false
  }
}

/**
 * 处理对战胜利
 */
async function handleBattleWin() {
  battleStatus.value = 'FINISHED'
  stopBattleTimer()
  stopRoomStatusCheck()

  try {
    // 调用后端API结束对战
    const response = await finishBattle({
      roomCode: props.roomInfo.roomCode,
      winnerId: currentUser.value.id,
      battleTime: battleTime.value,
      reason: 'SOLVED' // 标记为解题获胜
    })

    if (response.status) {
      ElMessage.success('恭喜！您成功解决了题目，获得胜利！')
      emit('battle-end', {
        winner: currentUser.value,
        loser: opponent.value,
        battleTime: battleTime.value,
        roomInfo: props.roomInfo,
        reason: 'SOLVED'
      })
    }

  } catch (error) {
    console.error('❌ 结束对战失败:', error)
    ElMessage.error('结束对战失败: ' + error.message)
  }
}







/**
 * 放弃对战
 */
function surrenderBattle() {
  ElMessageBox.confirm(
    '确定要放弃对战吗？放弃后将判定为失败。',
    '确认放弃对战',
    {
      confirmButtonText: '确定放弃',
      cancelButtonText: '继续对战',
      type: 'warning'
    }
  ).then(async () => {
    battleStatus.value = 'FINISHED'
    stopBattleTimer()
    stopRoomStatusCheck()

    try {
      // 调用后端API结束对战，对手获胜
      const response = await finishBattle({
        roomCode: props.roomInfo.roomCode,
        winnerId: opponent.value?.id,
        battleTime: battleTime.value,
        reason: 'SURRENDER' // 标记为主动投降
      })

      if (response.status) {
        // ElMessage.info('您已放弃对战，判定为失败')
        emit('battle-end', {
          winner: opponent.value,
          loser: currentUser.value,
          battleTime: battleTime.value,
          roomInfo: props.roomInfo,
          reason: 'SURRENDER'
        })
      }

    } catch (error) {
      console.error('❌ 结束对战失败:', error)
      ElMessage.error('结束对战失败: ' + error.message)
    }
  }).catch(() => {
    // 用户取消
  })
}

/**
 * 关闭对战界面
 */
function closeBattle() {
  if (battleStatus.value === 'ONGOING') {
    // 对战进行中，需要确认退出
    ElMessageBox.confirm(
      '对战正在进行中，退出房间将判定为失败！\n确定要退出吗？',
      '确认退出对战',
      {
        confirmButtonText: '确定退出',
        cancelButtonText: '继续对战',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    ).then(async () => {
      await handleExitRoom('QUIT', '您已退出对战，判定为失败')
    }).catch(() => {
      // 用户取消，继续对战
    })
  } else {
    // 非对战状态（等待、准备等），需要确认离开房间
    ElMessageBox.confirm(
      '确定要离开房间吗？',
      '确认离开',
      {
        confirmButtonText: '确定离开',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      await handleLeaveRoom()
    }).catch(() => {
      // 用户取消
    })
  }
}

/**
 * 处理退出房间（对战中）
 */
async function handleExitRoom(reason, message) {
  battleStatus.value = 'FINISHED'
  stopBattleTimer()
  stopRoomStatusCheck()

  try {
    // 调用后端API结束对战，对手获胜
    const response = await finishBattle({
      roomCode: props.roomInfo.roomCode,
      winnerId: opponent.value?.id,
      battleTime: battleTime.value,
      reason: reason
    })

    if (response.status) {
      ElMessage.warning(message)
      emit('battle-end', {
        winner: opponent.value,
        loser: currentUser.value,
        battleTime: battleTime.value,
        roomInfo: props.roomInfo,
        reason: reason
      })
    }

  } catch (error) {
    console.error('❌ 结束对战失败:', error)
    ElMessage.error('结束对战失败: ' + error.message)
  }

  emit('close')
}

/**
 * 处理离开房间（非对战状态）
 */
async function handleLeaveRoom() {
  stopBattleTimer()
  stopRoomStatusCheck()

  try {
    // 调用离开房间API
    const response = await leaveRoom({
      roomCode: props.roomInfo.roomCode,
      userId: currentUser.value.id
    })

    if (response.status) {
      ElMessage.success('已离开房间')
    } else {
      ElMessage.warning('离开房间: ' + (response.message || '操作完成'))
    }

  } catch (error) {
    console.error('❌ 离开房间失败:', error)
    ElMessage.error('离开房间失败: ' + error.message)
  }

  emit('close')
}

/**
 * 打开题目链接
 */
const openProblemLink = () => {
  if (problemUrl.value) {
    window.open(problemUrl.value, '_blank')
  } else {
    ElMessage.error('题目链接不可用')
  }
}

/**
 * 获取难度标签类型
 */
const getDifficultyType = (difficulty) => {
  if (!difficulty) return 'info'
  if (difficulty >= 2000) return 'danger'
  if (difficulty >= 1600) return 'warning'
  if (difficulty >= 1200) return 'primary'
  return 'success'
}

// ==================== WebSocket连接管理 ====================

/**
 * 连接WebSocket
 */
function connectWebSocket() {
  if (stompClient && stompClient.connected) {
    console.log('🔗 WebSocket已连接，跳过重复连接')
    return
  }

  try {
    const socket = new SockJS('http://localhost:8080/ws')
    stompClient = Stomp.over(socket)

    stompClient.connect({},
      (frame) => {
        console.log('🔗 WebSocket连接成功:', frame)

        // 订阅房间消息
        stompClient.subscribe(`/topic/room/${props.roomInfo.roomCode}`, (message) => {
          handleRoomMessage(JSON.parse(message.body))
        })


      },
      (error) => {
        console.error('❌ WebSocket连接失败:', error)
      }
    )
  } catch (error) {
    console.error('❌ WebSocket连接异常:', error)
  }
}

/**
 * 断开WebSocket连接
 */
function disconnectWebSocket() {
  if (stompClient && stompClient.connected) {
    stompClient.disconnect(() => {
      console.log('🔌 WebSocket连接已断开')
    })
  }
}

/**
 * 处理房间消息
 */
function handleRoomMessage(message) {
  console.log('📨 收到房间消息:', message)
  // 这里可以处理其他房间消息
}



// ==================== 生命周期钩子 ====================

onMounted(() => {
  if (props.visible) {
    startBattleTimer()
    startRoomStatusCheck()
    connectWebSocket()
  }
})

onUnmounted(() => {
  stopBattleTimer()
  stopRoomStatusCheck()
  disconnectWebSocket()
})

// 监听visible变化
watch(() => props.visible, (visible) => {
  if (visible) {
    battleTime.value = 0
    battleStatus.value = 'ONGOING'
    submissionStatus.value = {
      checking: false,
      lastCheck: null,
      isPassed: false
    }

    startBattleTimer()
    startRoomStatusCheck()
    connectWebSocket()
  } else {
    stopBattleTimer()
    stopRoomStatusCheck()
    disconnectWebSocket()
  }
})
</script>

<template>
  <el-dialog
    :model-value="visible"
    title="对战进行中"
    width="900px"
    :before-close="closeBattle"
    :close-on-click-modal="false"
    class="battle-dialog"
  >
    <div class="battle-room">
      <!-- 对战状态栏 -->
      <el-card class="battle-status-card" shadow="never">
        <div class="battle-status">
          <div class="status-item">
            <el-icon><Clock /></el-icon>
            <span>对战时间: {{ formatTime(battleTime) }}</span>
          </div>
          <div class="status-item">
            <el-tag :type="battleStatus === 'ONGOING' ? 'warning' : 'success'" size="large">
              {{ battleStatus === 'ONGOING' ? '进行中' : '已结束' }}
            </el-tag>
          </div>

        </div>
      </el-card>

      <!-- 题目信息 -->
      <el-card class="problem-card" shadow="never">
        <template #header>
          <div class="problem-header">
            <h3>{{ problem?.title || '题目加载中...' }}</h3>
            <el-button type="primary" @click="openProblemLink" :icon="Link">
              前往Codeforces做题
            </el-button>
          </div>
        </template>
        
        <div class="problem-info" v-if="problem">
          <div class="info-item">
            <span class="label">题目:</span>
            <span class="problem-id">{{ problem.contestId }}{{ problem.problemId }}</span>
          </div>
          <div class="info-item">
            <span class="label">难度:</span>
            <el-tag :type="getDifficultyType(problem.difficulty)">
              {{ problem.difficulty }}
            </el-tag>
          </div>
          <div class="info-item" v-if="problem.tags">
            <span class="label">标签:</span>
            <span>{{ problem.tags }}</span>
          </div>
        </div>
      </el-card>

      <!-- 对战双方状态 -->
      <div class="battle-participants">
        <!-- 当前用户 -->
        <el-card class="participant-card" shadow="never">
          <template #header>
            <div class="participant-header">
              <el-avatar :src="currentUser?.avatar" :size="40" />
              <div class="participant-info">
                <h4>{{ currentUser?.codeforcesId }}</h4>
                <div class="participant-details">
                  <span class="participant-role">您</span>
                  <span class="participant-rating">
                    <el-icon><Trophy /></el-icon>
                    {{ currentUser?.rating || 'Unrated' }}
                  </span>
                </div>
              </div>
            </div>
          </template>
          
          <div class="participant-status">
            <div class="status-indicator" :class="{ 'passed': submissionStatus.isPassed }">
              <el-icon v-if="submissionStatus.isPassed"><Check /></el-icon>
              <span>{{ submissionStatus.isPassed ? '已通过' : '未通过' }}</span>
            </div>
            
            <div class="action-buttons">
              <el-button
                type="success"
                @click="checkSubmission"
                :loading="submissionStatus.checking"
                :disabled="battleStatus !== 'ONGOING' || submissionStatus.isPassed"
                :icon="Refresh"
              >
                检查提交状态
              </el-button>

              <el-button
                type="danger"
                @click="surrenderBattle"
                :disabled="battleStatus !== 'ONGOING'"
                :icon="Flag"
              >
                放弃对战
              </el-button>
            </div>
            
            <div class="last-check" v-if="submissionStatus.lastCheck">
              <small>最后检查: {{ submissionStatus.lastCheck.toLocaleTimeString() }}</small>
            </div>
          </div>
        </el-card>

        <!-- 对手 -->
        <el-card class="participant-card" shadow="never">
          <template #header>
            <div class="participant-header">
              <el-avatar :src="opponent?.avatar" :size="40" />
              <div class="participant-info">
                <h4>{{ opponent?.codeforcesId || '对手' }}</h4>
                <div class="participant-details">
                  <span class="participant-role">对手</span>
                  <span class="participant-rating">
                    <el-icon><Trophy /></el-icon>
                    {{ opponent?.rating || 'Unrated' }}
                  </span>
                </div>
              </div>
            </div>
          </template>
          
          <div class="participant-status">
            <div class="status-indicator" :class="{ 'passed': opponentStatus.isPassed }">
              <el-icon v-if="opponentStatus.isPassed"><Check /></el-icon>
              <span>{{ opponentStatus.isPassed ? '已通过' : '未通过' }}</span>
            </div>
            
            <div class="opponent-info">
              <p>对手正在努力解题中...</p>
              <small>系统会自动检测对手的提交状态</small>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 对战说明 -->
      <el-card class="battle-instructions" shadow="never">
        <template #header>
          <h4>对战说明</h4>
        </template>
        
        <div class="instructions-content">
          <ol>
            <li>点击"前往Codeforces做题"按钮在新窗口中打开题目</li>
            <li>在Codeforces网站上完成题目并提交代码</li>
            <li>回到本页面点击"检查提交状态"验证是否通过</li>
            <li>首先通过题目的选手获得胜利</li>
            <li>如果遇到困难可以选择"放弃对战"</li>
          </ol>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="closeBattle"
          :icon="Close"
          :type="battleStatus === 'ONGOING' ? 'danger' : 'default'"
        >
          {{ battleStatus === 'ONGOING' ? '退出对战 (判定失败)' : '关闭' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.battle-dialog {
  .battle-room {
    .battle-status-card {
      margin-bottom: 20px;

      .battle-status {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .status-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;

          .el-icon {
            color: #409eff;
          }
        }
      }
    }

    .problem-card {
      margin-bottom: 20px;

      .problem-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          color: #303133;
        }
      }

      .problem-info {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .label {
            font-weight: 500;
            color: #606266;
            min-width: 60px;
          }

          .problem-id {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #409eff;
            font-size: 16px;
          }
        }
      }
    }

    .battle-participants {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;

      .participant-card {
        .participant-header {
          display: flex;
          align-items: center;
          gap: 12px;

          .participant-info {
            h4 {
              margin: 0 0 4px 0;
              color: #303133;
            }

            .participant-details {
              display: flex;
              align-items: center;
              gap: 12px;

              .participant-role {
                font-size: 12px;
                color: #909399;
              }

              .participant-rating {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #f56c6c;
                font-weight: 500;
              }
            }
          }
        }

        .participant-status {
          .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border-radius: 6px;
            background: #f5f7fa;
            margin-bottom: 16px;
            font-weight: 500;

            &.passed {
              background: #f0f9ff;
              color: #67c23a;
              border: 1px solid #b3e19d;
            }

            .el-icon {
              color: #67c23a;
            }
          }

          .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 12px;

            .el-button {
              width: 100%;
              margin: 0 !important;
            }

          }

          .opponent-info {
            text-align: center;
            color: #606266;

            p {
              margin: 0 0 8px 0;
            }

            small {
              color: #909399;
            }
          }

          .last-check {
            text-align: center;

            small {
              color: #909399;
            }
          }
        }
      }
    }

    .battle-instructions {
      .instructions-content {
        ol {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            line-height: 1.5;
            color: #606266;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}


</style>
