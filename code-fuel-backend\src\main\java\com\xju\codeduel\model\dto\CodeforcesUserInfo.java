package com.xju.codeduel.model.dto;

/**
 * Codeforces用户信息数据传输对象（DTO）
 *
 * 设计目的：
 * 1. 封装从Codeforces API获取的用户完整信息
 * 2. 提供类型安全的数据访问
 * 3. 隐藏外部API的数据结构细节
 * 4. 便于在Java系统内部传递用户数据
 *
 * 数据来源：
 * - 通过Python服务调用Codeforces官方API获取
 * - API地址：https://codeforces.com/api/user.info?handles={用户名}
 *
 * 使用场景：
 * - 用户注册时获取头像、Rating等信息
 * - 验证用户身份时比较firstName字段
 * - 在系统内部传递Codeforces用户数据
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public class CodeforcesUserInfo {

    /**
     * 用户句柄（用户名）
     */
    private String handle;

    /**
     * 用户的firstName字段（用于验证）
     */
    private String firstName;

    /**
     * 用户头像URL
     */
    private String avatar;

    /**
     * 用户标题图片URL
     */
    private String titlePhoto;

    /**
     * 用户当前Rating
     */
    private Integer rating;

    /**
     * 用户最高Rating
     */
    private Integer maxRating;

    /**
     * 用户国家
     */
    private String country;

    /**
     * 用户组织/学校
     */
    private String organization;

    /**
     * 用户等级（如expert, candidate master等）
     */
    private String rank;

    public CodeforcesUserInfo() {}

    public CodeforcesUserInfo(String handle, String firstName, String avatar, String titlePhoto, 
                             Integer rating, Integer maxRating, String country, String organization, String rank) {
        this.handle = handle;
        this.firstName = firstName;
        this.avatar = avatar;
        this.titlePhoto = titlePhoto;
        this.rating = rating;
        this.maxRating = maxRating;
        this.country = country;
        this.organization = organization;
        this.rank = rank;
    }

    // Getter和Setter方法

    public String getHandle() {
        return handle;
    }

    public void setHandle(String handle) {
        this.handle = handle;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getTitlePhoto() {
        return titlePhoto;
    }

    public void setTitlePhoto(String titlePhoto) {
        this.titlePhoto = titlePhoto;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public Integer getMaxRating() {
        return maxRating;
    }

    public void setMaxRating(Integer maxRating) {
        this.maxRating = maxRating;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    @Override
    public String toString() {
        return "CodeforcesUserInfo{" +
                "handle='" + handle + '\'' +
                ", firstName='" + firstName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", titlePhoto='" + titlePhoto + '\'' +
                ", rating=" + rating +
                ", maxRating=" + maxRating +
                ", country='" + country + '\'' +
                ", organization='" + organization + '\'' +
                ", rank='" + rank + '\'' +
                '}';
    }
}
