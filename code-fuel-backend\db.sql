/*==============================================================*/
/* DBMS name:      MySQL 5.0                                    */
/* Created on:     2025/7/14 22:25:38                           */
/*==============================================================*/
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS codeduel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用指定数据库
USE codeduel;


drop table if exists battle_records;

drop table if exists chat_messages;

drop table if exists comments;

drop table if exists posts;

drop table if exists problems;

drop table if exists problems_tags;

drop table if exists tags;

drop table if exists user_battle_record;

drop table if exists user_rating_histories;

drop table if exists users;

/*==============================================================*/
/* Table: battle_records                                        */
/*==============================================================*/
create table battle_records
(
    id                   bigint not null auto_increment comment '主键',
    problem_id           bigint not null comment '题目信息ID',
    is_room              smallint not null comment '是否是开房间（0-匹配 1-开房间）',
    start_time           datetime not null default CURRENT_TIMESTAMP comment '对战开始时间',
    end_time             datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '对战结束时间',
    room_code            bigint not null comment '房间码',
    primary key (id)
);

alter table battle_records comment '对战记录';

/*==============================================================*/
/* Table: chat_messages                                         */
/*==============================================================*/
create table chat_messages
(
    id                   bigint not null auto_increment comment '主键',
    user_id              bigint not null comment '用户ID',
    content              text not null comment '消息内容',
    sent_time            datetime not null default CURRENT_TIMESTAMP comment '发送时间',
    is_deleted           smallint not null default 0 comment '逻辑删除（0正常1删除）',
    primary key (id)
);

alter table chat_messages comment '聊天消息';

/*==============================================================*/
/* Table: comments                                              */
/*==============================================================*/
create table comments
(
    id                   bigint not null auto_increment comment '主键',
    post_id              bigint not null comment '帖子ID',
    user_id              bigint not null comment '用户ID',
    parent_id            bigint comment '父评论ID',
    content              text not null comment '评论内容',
    create_time          datetime not null default CURRENT_TIMESTAMP comment '评论时间',
    is_deleted           smallint not null default 0 comment '逻辑删除(0正常，1删除)',
    primary key (id)
);

alter table comments comment '评论';

/*==============================================================*/
/* Table: posts                                                 */
/*==============================================================*/
create table posts
(
    id                   bigint not null auto_increment comment '主键',
    user_id              bigint not null comment '用户ID',
    title                varchar(100) not null comment '标题',
    content              text not null comment '内容',
    is_top               smallint not null default 0 comment '置顶标志（0正常1置顶）',
    post_time            datetime not null default CURRENT_TIMESTAMP comment '发帖时间',
    update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '修改时间',
    is_deleted           smallint not null default 0 comment '逻辑删除 (0:正常, 1:删除)',
    like_count           int not null default 0 comment '点赞数',
    primary key (id)
);

alter table posts comment '帖子';

/*==============================================================*/
/* Table: problems                                              */
/*==============================================================*/
create table problems
(
    id                   bigint not null auto_increment comment '主键',
    title                varchar(200) not null comment '题目标题',
    difficulty           int not null comment '题目难度值',
    contest_id           int not null comment '比赛ID',
    problem_id           varchar(50) not null comment '题目ID',
    created_time         datetime not null default CURRENT_TIMESTAMP comment '创建时间',
    update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    is_deleted           smallint not null default 0 comment '逻辑删除 (0:正常, 1:删除)',
    primary key (id)
);

alter table problems comment '题目信息';

/*==============================================================*/
/* Table: problems_tags                                         */
/*==============================================================*/
create table problems_tags
(
    id                   bigint not null auto_increment comment '主键',
    tag_id               bigint not null comment '标签ID',
    problem_id           bigint not null comment '题目信息ID',
    primary key (id)
);

alter table problems_tags comment '题目标签';

/*==============================================================*/
/* Table: tags                                                  */
/*==============================================================*/
create table tags
(
    id                   bigint not null auto_increment comment '主键',
    tag_name             varchar(50) not null comment '标签名',
    created_time         datetime not null comment '创建时间',
    primary key (id)
);

alter table tags comment '标签';

/*==============================================================*/
/* Table: user_battle_record                                    */
/*==============================================================*/
create table user_battle_record
(
    id                   bigint not null auto_increment comment '主键',
    user_id              bigint not null comment '用户ID',
    battle_id            bigint not null comment '对战记录ID',
    primary key (id)
);

alter table user_battle_record comment '用户的对战记录';

/*==============================================================*/
/* Table: user_rating_histories                                 */
/*==============================================================*/
create table user_rating_histories
(
    id                   bigint not null auto_increment comment '主键',
    user_id              bigint not null comment '用户ID',
    battle_id            bigint not null comment '对战记录ID',
    old_rating           int not null comment '变化前Rating',
    new_rating           int not null comment '变化后Rating',
    reason               varchar(100) not null comment '变化原因',
    record_time          datetime not null default CURRENT_TIMESTAMP comment '记录时间',
    primary key (id)
);

alter table user_rating_histories comment '用户Rating历史表';

/*==============================================================*/
/* Table: users                                                 */
/*==============================================================*/
create table users
(
    id                   bigint not null auto_increment comment '主键',
    password             varchar(100) not null comment '加密密码',
    codeforces_id        varchar(50) not null comment 'Codeforces_ID(唯一)',
    avatar               varchar(200) not null comment '头像路径',
    rating               int not null default 1500 comment '当前Rating（默认1500）',
    status               smallint not null default 0 comment '状态(0正常1封禁)',
    ban_reason           varchar(200) comment '封禁原因 (当status=1时有效)',
    register_time        datetime not null default CURRENT_TIMESTAMP comment '注册时间',
    update_time          datetime not null default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '最后修改时间',
    is_admin             smallint not null default 0 comment '管理员标识',
    last_login           DATETIME COMMENT '最后登录时间（可为空）',
    primary key (id)
);

alter table users comment '用户';

alter table battle_records add constraint FK_battle_problems foreign key (problem_id)
    references problems (id) on delete restrict on update restrict;

alter table chat_messages add constraint FK_user_send_message foreign key (user_id)
    references users (id) on delete restrict on update restrict;

alter table comments add constraint FK_comment_post foreign key (post_id)
    references posts (id) on delete restrict on update restrict;

alter table comments add constraint FK_parent_comment foreign key (parent_id)
    references comments (id) on delete restrict on update restrict;

alter table comments add constraint FK_user_comment foreign key (user_id)
    references users (id) on delete restrict on update restrict;

alter table posts add constraint FK_user_post foreign key (user_id)
    references users (id) on delete restrict on update restrict;

alter table problems_tags add constraint FK_problems_tags foreign key (tag_id)
    references tags (id) on delete restrict on update restrict;

alter table problems_tags add constraint FK_problems_tags2 foreign key (problem_id)
    references problems (id) on delete restrict on update restrict;

alter table user_battle_record add constraint FK_user_battle_record foreign key (user_id)
    references users (id) on delete restrict on update restrict;

alter table user_battle_record add constraint FK_user_battle_record2 foreign key (battle_id)
    references battle_records (id) on delete restrict on update restrict;

alter table user_rating_histories add constraint FK_battle_change_rating foreign key (battle_id)
    references battle_records (id) on delete restrict on update restrict;

alter table user_rating_histories add constraint FK_user_rating_change foreign key (user_id)
    references users (id) on delete restrict on update restrict;

-- ========================================
-- 插入测试数据
-- ========================================

-- 插入20个用户
INSERT INTO users (id, password, codeforces_id, avatar, rating, status, ban_reason, register_time, update_time, is_admin, last_login) VALUES
                                                                                                                                          (1, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'tourist', '/avatars/tourist.jpg', 3500, 0, NULL, '2024-01-15 10:30:00', '2025-07-20 14:20:00', 0, '2025-07-20 14:20:00'),
                                                                                                                                          (2, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'jiangly', '/avatars/jiangly.jpg', 3400, 0, NULL, '2024-01-20 11:15:00', '2025-07-20 13:45:00', 0, '2025-07-20 13:45:00'),
                                                                                                                                          (3, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'Benq', '/avatars/benq.jpg', 3300, 0, NULL, '2024-02-01 09:20:00', '2025-07-20 12:30:00', 0, '2025-07-20 12:30:00'),
                                                                                                                                          (4, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'Um_nik', '/avatars/um_nik.jpg', 3200, 0, NULL, '2024-02-10 16:45:00', '2025-07-20 11:15:00', 0, '2025-07-20 11:15:00'),
                                                                                                                                          (5, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'ecnerwala', '/avatars/ecnerwala.jpg', 3100, 0, NULL, '2024-02-15 14:30:00', '2025-07-20 10:45:00', 0, '2025-07-20 10:45:00'),
                                                                                                                                          (6, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'Radewoosh', '/avatars/radewoosh.jpg', 2900, 0, NULL, '2024-03-01 13:20:00', '2025-07-20 09:30:00', 0, '2025-07-20 09:30:00'),
                                                                                                                                          (7, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'maroonrk', '/avatars/maroonrk.jpg', 2800, 0, NULL, '2024-03-10 12:15:00', '2025-07-20 08:45:00', 0, '2025-07-20 08:45:00'),
                                                                                                                                          (8, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'ksun48', '/avatars/ksun48.jpg', 2700, 0, NULL, '2024-03-20 11:30:00', '2025-07-20 07:20:00', 0, '2025-07-20 07:20:00'),
                                                                                                                                          (9, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'Petr', '/avatars/petr.jpg', 2600, 0, NULL, '2024-04-01 10:45:00', '2025-07-19 20:15:00', 0, '2025-07-19 20:15:00'),
                                                                                                                                          (10, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'scott_wu', '/avatars/scott_wu.jpg', 2500, 0, NULL, '2024-04-10 09:30:00', '2025-07-19 19:30:00', 0, '2025-07-19 19:30:00'),
                                                                                                                                          (11, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'mnbvmar', '/avatars/mnbvmar.jpg', 2400, 0, NULL, '2024-04-20 08:15:00', '2025-07-19 18:45:00', 0, '2025-07-19 18:45:00'),
                                                                                                                                          (12, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'Errichto', '/avatars/errichto.jpg', 2300, 0, NULL, '2024-05-01 15:20:00', '2025-07-19 17:30:00', 0, '2025-07-19 17:30:00'),
                                                                                                                                          (13, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'SecondThread', '/avatars/secondthread.jpg', 2200, 0, NULL, '2024-05-10 14:45:00', '2025-07-19 16:15:00', 0, '2025-07-19 16:15:00'),
                                                                                                                                          (14, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'tmwilliamlin168', '/avatars/tmwilliamlin168.jpg', 2100, 0, NULL, '2024-05-20 13:30:00', '2025-07-19 15:45:00', 0, '2025-07-19 15:45:00'),
                                                                                                                                          (15, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'neal', '/avatars/neal.jpg', 2000, 0, NULL, '2024-06-01 12:15:00', '2025-07-19 14:30:00', 0, '2025-07-19 14:30:00'),
                                                                                                                                          (16, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'rainboy', '/avatars/rainboy.jpg', 1900, 0, NULL, '2024-06-10 11:45:00', '2025-07-19 13:15:00', 0, '2025-07-19 13:15:00'),
                                                                                                                                          (17, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'conqueror_of_tourist', '/avatars/conqueror_of_tourist.jpg', 1800, 0, NULL, '2024-06-20 10:30:00', '2025-07-19 12:45:00', 0, '2025-07-19 12:45:00'),
                                                                                                                                          (18, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'awoo', '/avatars/awoo.jpg', 1700, 0, NULL, '2024-07-01 09:15:00', '2025-07-19 11:30:00', 0, '2025-07-19 11:30:00'),
                                                                                                                                          (19, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'newbie_coder', '/avatars/newbie_coder.jpg', 1600, 0, NULL, '2024-07-10 08:45:00', '2025-07-19 10:15:00', 0, '2025-07-19 10:15:00'),
                                                                                                                                          (20, '$2a$10$N.zmdr9k7uOCQb0q8qzvW.aRuIkJrjic2gMVkMy6TX/hpnpfDAjVG', 'admin', '/avatars/admin.jpg', 2000, 0, NULL, '2024-01-01 00:00:00', '2025-07-20 15:00:00', 1, '2025-07-20 15:00:00');

-- 插入标签数据
INSERT INTO tags (id, tag_name, created_time) VALUES
                                                  (1, '动态规划', '2024-01-01 00:00:00'),
                                                  (2, '贪心算法', '2024-01-01 00:00:00'),
                                                  (3, '图论', '2024-01-01 00:00:00'),
                                                  (4, '数据结构', '2024-01-01 00:00:00'),
                                                  (5, '字符串', '2024-01-01 00:00:00'),
                                                  (6, '数学', '2024-01-01 00:00:00'),
                                                  (7, '二分查找', '2024-01-01 00:00:00'),
                                                  (8, '排序', '2024-01-01 00:00:00'),
                                                  (9, '树', '2024-01-01 00:00:00'),
                                                  (10, '递归', '2024-01-01 00:00:00'),
                                                  (11, '分治', '2024-01-01 00:00:00'),
                                                  (12, '回溯', '2024-01-01 00:00:00');

-- 插入题目数据
INSERT INTO problems (id, title, difficulty, contest_id, problem_id, created_time, update_time, is_deleted) VALUES
                                                                                                                (1, 'Two Sum', 800, 1001, 'A', '2024-01-01 10:00:00', '2024-01-01 10:00:00', 0),
                                                                                                                (2, 'Maximum Subarray', 1200, 1001, 'B', '2024-01-01 10:00:00', '2024-01-01 10:00:00', 0),
                                                                                                                (3, 'Longest Common Subsequence', 1500, 1002, 'A', '2024-01-02 10:00:00', '2024-01-02 10:00:00', 0),
                                                                                                                (4, 'Binary Tree Traversal', 1000, 1002, 'B', '2024-01-02 10:00:00', '2024-01-02 10:00:00', 0),
                                                                                                                (5, 'Graph Shortest Path', 1800, 1003, 'A', '2024-01-03 10:00:00', '2024-01-03 10:00:00', 0),
                                                                                                                (6, 'String Matching', 1300, 1003, 'B', '2024-01-03 10:00:00', '2024-01-03 10:00:00', 0),
                                                                                                                (7, 'Matrix Chain Multiplication', 2000, 1004, 'A', '2024-01-04 10:00:00', '2024-01-04 10:00:00', 0),
                                                                                                                (8, 'Knapsack Problem', 1600, 1004, 'B', '2024-01-04 10:00:00', '2024-01-04 10:00:00', 0),
                                                                                                                (9, 'Minimum Spanning Tree', 1700, 1005, 'A', '2024-01-05 10:00:00', '2024-01-05 10:00:00', 0),
                                                                                                                (10, 'Quick Sort Implementation', 900, 1005, 'B', '2024-01-05 10:00:00', '2024-01-05 10:00:00', 0),
                                                                                                                (11, 'Fibonacci Sequence', 600, 1006, 'A', '2024-01-06 10:00:00', '2024-01-06 10:00:00', 0),
                                                                                                                (12, 'Binary Search Tree', 1400, 1006, 'B', '2024-01-06 10:00:00', '2024-01-06 10:00:00', 0),
                                                                                                                (13, 'Topological Sort', 1900, 1007, 'A', '2024-01-07 10:00:00', '2024-01-07 10:00:00', 0),
                                                                                                                (14, 'Palindrome Check', 700, 1007, 'B', '2024-01-07 10:00:00', '2024-01-07 10:00:00', 0),
                                                                                                                (15, 'Dijkstra Algorithm', 2100, 1008, 'A', '2024-01-08 10:00:00', '2024-01-08 10:00:00', 0);

-- 插入题目标签关联
INSERT INTO problems_tags (id, tag_id, problem_id) VALUES
                                                       (1, 6, 1), (2, 8, 1),
                                                       (3, 1, 2), (4, 2, 2),
                                                       (5, 1, 3), (6, 5, 3),
                                                       (7, 4, 4), (8, 9, 4), (9, 10, 4),
                                                       (10, 3, 5), (11, 7, 5),
                                                       (12, 5, 6), (13, 7, 6),
                                                       (14, 1, 7), (15, 11, 7),
                                                       (16, 1, 8), (17, 2, 8),
                                                       (18, 3, 9), (19, 4, 9),
                                                       (20, 8, 10), (21, 11, 10),
                                                       (22, 1, 11), (23, 10, 11),
                                                       (24, 4, 12), (25, 9, 12),
                                                       (26, 3, 13), (27, 4, 13),
                                                       (28, 5, 14), (29, 6, 14),
                                                       (30, 3, 15), (31, 2, 15);

-- 插入60个对战记录
INSERT INTO battle_records (id, problem_id, is_room, start_time, end_time, room_code) VALUES
                                                                                          (1, 1, 0, '2025-07-01 10:00:00', '2025-07-01 10:15:00', 100001),
                                                                                          (2, 2, 0, '2025-07-01 11:00:00', '2025-07-01 11:20:00', 100002),
                                                                                          (3, 3, 1, '2025-07-01 14:00:00', '2025-07-01 14:25:00', 100003),
                                                                                          (4, 4, 0, '2025-07-01 15:30:00', '2025-07-01 15:45:00', 100004),
                                                                                          (5, 5, 0, '2025-07-01 16:00:00', '2025-07-01 16:30:00', 100005),
                                                                                          (6, 1, 1, '2025-07-02 09:00:00', '2025-07-02 09:12:00', 100006),
                                                                                          (7, 6, 0, '2025-07-02 10:30:00', '2025-07-02 10:50:00', 100007),
                                                                                          (8, 7, 0, '2025-07-02 13:00:00', '2025-07-02 13:35:00', 100008),
                                                                                          (9, 2, 1, '2025-07-02 14:15:00', '2025-07-02 14:30:00', 100009),
                                                                                          (10, 8, 0, '2025-07-02 16:45:00', '2025-07-02 17:10:00', 100010),
                                                                                          (11, 3, 0, '2025-07-03 08:30:00', '2025-07-03 08:55:00', 100011),
                                                                                          (12, 9, 0, '2025-07-03 11:15:00', '2025-07-03 11:40:00', 100012),
                                                                                          (13, 4, 1, '2025-07-03 13:45:00', '2025-07-03 14:00:00', 100013),
                                                                                          (14, 10, 0, '2025-07-03 15:20:00', '2025-07-03 15:35:00', 100014),
                                                                                          (15, 5, 0, '2025-07-03 17:00:00', '2025-07-03 17:30:00', 100015),
                                                                                          (16, 11, 0, '2025-07-04 09:15:00', '2025-07-04 09:25:00', 100016),
                                                                                          (17, 6, 1, '2025-07-04 10:45:00', '2025-07-04 11:05:00', 100017),
                                                                                          (18, 12, 0, '2025-07-04 14:30:00', '2025-07-04 14:50:00', 100018),
                                                                                          (19, 7, 0, '2025-07-04 16:00:00', '2025-07-04 16:35:00', 100019),
                                                                                          (20, 13, 0, '2025-07-04 18:15:00', '2025-07-04 18:45:00', 100020),
                                                                                          (21, 8, 1, '2025-07-05 08:00:00', '2025-07-05 08:25:00', 100021),
                                                                                          (22, 14, 0, '2025-07-05 10:30:00', '2025-07-05 10:40:00', 100022),
                                                                                          (23, 9, 0, '2025-07-05 12:15:00', '2025-07-05 12:40:00', 100023),
                                                                                          (24, 15, 0, '2025-07-05 15:45:00', '2025-07-05 16:20:00', 100024),
                                                                                          (25, 1, 0, '2025-07-05 17:30:00', '2025-07-05 17:45:00', 100025),
                                                                                          (26, 10, 1, '2025-07-06 09:45:00', '2025-07-06 10:00:00', 100026),
                                                                                          (27, 2, 0, '2025-07-06 11:20:00', '2025-07-06 11:40:00', 100027),
                                                                                          (28, 11, 0, '2025-07-06 13:00:00', '2025-07-06 13:10:00', 100028),
                                                                                          (29, 3, 1, '2025-07-06 14:45:00', '2025-07-06 15:10:00', 100029),
                                                                                          (30, 12, 0, '2025-07-06 16:30:00', '2025-07-06 16:50:00', 100030),
                                                                                          (31, 4, 0, '2025-07-07 08:15:00', '2025-07-07 08:30:00', 100031),
                                                                                          (32, 13, 1, '2025-07-07 10:00:00', '2025-07-07 10:30:00', 100032),
                                                                                          (33, 5, 0, '2025-07-07 12:45:00', '2025-07-07 13:15:00', 100033),
                                                                                          (34, 14, 0, '2025-07-07 15:20:00', '2025-07-07 15:30:00', 100034),
                                                                                          (35, 6, 1, '2025-07-07 17:00:00', '2025-07-07 17:20:00', 100035),
                                                                                          (36, 15, 0, '2025-07-08 09:30:00', '2025-07-08 10:05:00', 100036),
                                                                                          (37, 7, 0, '2025-07-08 11:15:00', '2025-07-08 11:50:00', 100037),
                                                                                          (38, 1, 1, '2025-07-08 13:30:00', '2025-07-08 13:45:00', 100038),
                                                                                          (39, 8, 0, '2025-07-08 15:45:00', '2025-07-08 16:10:00', 100039),
                                                                                          (40, 2, 0, '2025-07-08 17:20:00', '2025-07-08 17:40:00', 100040),
                                                                                          (41, 9, 1, '2025-07-09 08:45:00', '2025-07-09 09:10:00', 100041),
                                                                                          (42, 10, 0, '2025-07-09 10:30:00', '2025-07-09 10:45:00', 100042),
                                                                                          (43, 3, 0, '2025-07-09 12:00:00', '2025-07-09 12:25:00', 100043),
                                                                                          (44, 11, 1, '2025-07-09 14:15:00', '2025-07-09 14:25:00', 100044),
                                                                                          (45, 4, 0, '2025-07-09 16:00:00', '2025-07-09 16:15:00', 100045),
                                                                                          (46, 12, 0, '2025-07-10 09:00:00', '2025-07-10 09:20:00', 100046),
                                                                                          (47, 5, 1, '2025-07-10 11:30:00', '2025-07-10 12:00:00', 100047),
                                                                                          (48, 13, 0, '2025-07-10 13:45:00', '2025-07-10 14:15:00', 100048),
                                                                                          (49, 6, 0, '2025-07-10 15:30:00', '2025-07-10 15:50:00', 100049),
                                                                                          (50, 14, 1, '2025-07-10 17:15:00', '2025-07-10 17:25:00', 100050),
                                                                                          (51, 7, 0, '2025-07-11 08:30:00', '2025-07-11 09:05:00', 100051),
                                                                                          (52, 15, 0, '2025-07-11 10:45:00', '2025-07-11 11:20:00', 100052),
                                                                                          (53, 8, 1, '2025-07-11 13:00:00', '2025-07-11 13:25:00', 100053),
                                                                                          (54, 1, 0, '2025-07-11 14:45:00', '2025-07-11 15:00:00', 100054),
                                                                                          (55, 9, 0, '2025-07-11 16:30:00', '2025-07-11 16:55:00', 100055),
                                                                                          (56, 2, 1, '2025-07-12 09:15:00', '2025-07-12 09:35:00', 100056),
                                                                                          (57, 10, 0, '2025-07-12 11:00:00', '2025-07-12 11:15:00', 100057),
                                                                                          (58, 3, 0, '2025-07-12 13:30:00', '2025-07-12 13:55:00', 100058),
                                                                                          (59, 11, 1, '2025-07-12 15:45:00', '2025-07-12 15:55:00', 100059),
                                                                                          (60, 12, 0, '2025-07-12 17:20:00', '2025-07-12 17:40:00', 100060);

-- 插入用户对战记录关联（每场对战2个用户，共120条记录）
INSERT INTO user_battle_record (id, user_id, battle_id) VALUES
                                                            (1, 1, 1), (2, 2, 1),
                                                            (3, 3, 2), (4, 4, 2),
                                                            (5, 5, 3), (6, 6, 3),
                                                            (7, 7, 4), (8, 8, 4),
                                                            (9, 9, 5), (10, 10, 5),
                                                            (11, 11, 6), (12, 12, 6),
                                                            (13, 13, 7), (14, 14, 7),
                                                            (15, 15, 8), (16, 16, 8),
                                                            (17, 17, 9), (18, 18, 9),
                                                            (19, 19, 10), (20, 1, 10),
                                                            (21, 2, 11), (22, 3, 11),
                                                            (23, 4, 12), (24, 5, 12),
                                                            (25, 6, 13), (26, 7, 13),
                                                            (27, 8, 14), (28, 9, 14),
                                                            (29, 10, 15), (30, 11, 15),
                                                            (31, 12, 16), (32, 13, 16),
                                                            (33, 14, 17), (34, 15, 17),
                                                            (35, 16, 18), (36, 17, 18),
                                                            (37, 18, 19), (38, 19, 19),
                                                            (39, 20, 20), (40, 1, 20),
                                                            (41, 2, 21), (42, 3, 21),
                                                            (43, 4, 22), (44, 5, 22),
                                                            (45, 6, 23), (46, 7, 23),
                                                            (47, 8, 24), (48, 9, 24),
                                                            (49, 10, 25), (50, 11, 25),
                                                            (51, 12, 26), (52, 13, 26),
                                                            (53, 14, 27), (54, 15, 27),
                                                            (55, 16, 28), (56, 17, 28),
                                                            (57, 18, 29), (58, 19, 29),
                                                            (59, 20, 30), (60, 1, 30),
                                                            (61, 2, 31), (62, 3, 31),
                                                            (63, 4, 32), (64, 5, 32),
                                                            (65, 6, 33), (66, 7, 33),
                                                            (67, 8, 34), (68, 9, 34),
                                                            (69, 10, 35), (70, 11, 35),
                                                            (71, 12, 36), (72, 13, 36),
                                                            (73, 14, 37), (74, 15, 37),
                                                            (75, 16, 38), (76, 17, 38),
                                                            (77, 18, 39), (78, 19, 39),
                                                            (79, 20, 40), (80, 1, 40),
                                                            (81, 2, 41), (82, 3, 41),
                                                            (83, 4, 42), (84, 5, 42),
                                                            (85, 6, 43), (86, 7, 43),
                                                            (87, 8, 44), (88, 9, 44),
                                                            (89, 10, 45), (90, 11, 45),
                                                            (91, 12, 46), (92, 13, 46),
                                                            (93, 14, 47), (94, 15, 47),
                                                            (95, 16, 48), (96, 17, 48),
                                                            (97, 18, 49), (98, 19, 49),
                                                            (99, 20, 50), (100, 1, 50),
                                                            (101, 2, 51), (102, 3, 51),
                                                            (103, 4, 52), (104, 5, 52),
                                                            (105, 6, 53), (106, 7, 53),
                                                            (107, 8, 54), (108, 9, 54),
                                                            (109, 10, 55), (110, 11, 55),
                                                            (111, 12, 56), (112, 13, 56),
                                                            (113, 14, 57), (114, 15, 57),
                                                            (115, 16, 58), (116, 17, 58),
                                                            (117, 18, 59), (118, 19, 59),
                                                            (119, 20, 60), (120, 1, 60);

-- 插入用户Rating历史变化记录（每场对战后参与用户的Rating变化，共120条记录）
INSERT INTO user_rating_histories (id, user_id, battle_id, old_rating, new_rating, reason, record_time) VALUES
                                                                                                            (1, 1, 1, 3480, 3500, '对战胜利', '2025-07-01 10:15:00'),
                                                                                                            (2, 2, 1, 3420, 3400, '对战失败', '2025-07-01 10:15:00'),
                                                                                                            (3, 3, 2, 3280, 3300, '对战胜利', '2025-07-01 11:20:00'),
                                                                                                            (4, 4, 2, 3220, 3200, '对战失败', '2025-07-01 11:20:00'),
                                                                                                            (5, 5, 3, 3080, 3100, '对战胜利', '2025-07-01 14:25:00'),
                                                                                                            (6, 6, 3, 2920, 2900, '对战失败', '2025-07-01 14:25:00'),
                                                                                                            (7, 7, 4, 2780, 2800, '对战胜利', '2025-07-01 15:45:00'),
                                                                                                            (8, 8, 4, 2720, 2700, '对战失败', '2025-07-01 15:45:00'),
                                                                                                            (9, 9, 5, 2580, 2600, '对战胜利', '2025-07-01 16:30:00'),
                                                                                                            (10, 10, 5, 2520, 2500, '对战失败', '2025-07-01 16:30:00'),
                                                                                                            (11, 11, 6, 2380, 2400, '对战胜利', '2025-07-02 09:12:00'),
                                                                                                            (12, 12, 6, 2320, 2300, '对战失败', '2025-07-02 09:12:00'),
                                                                                                            (13, 13, 7, 2180, 2200, '对战胜利', '2025-07-02 10:50:00'),
                                                                                                            (14, 14, 7, 2120, 2100, '对战失败', '2025-07-02 10:50:00'),
                                                                                                            (15, 15, 8, 1980, 2000, '对战胜利', '2025-07-02 13:35:00'),
                                                                                                            (16, 16, 8, 1920, 1900, '对战失败', '2025-07-02 13:35:00'),
                                                                                                            (17, 17, 9, 1780, 1800, '对战胜利', '2025-07-02 14:30:00'),
                                                                                                            (18, 18, 9, 1720, 1700, '对战失败', '2025-07-02 14:30:00'),
                                                                                                            (19, 19, 10, 1580, 1600, '对战胜利', '2025-07-02 17:10:00'),
                                                                                                            (20, 1, 10, 3500, 3480, '对战失败', '2025-07-02 17:10:00'),
                                                                                                            (21, 2, 11, 3400, 3420, '对战胜利', '2025-07-03 08:55:00'),
                                                                                                            (22, 3, 11, 3300, 3280, '对战失败', '2025-07-03 08:55:00'),
                                                                                                            (23, 4, 12, 3200, 3220, '对战胜利', '2025-07-03 11:40:00'),
                                                                                                            (24, 5, 12, 3100, 3080, '对战失败', '2025-07-03 11:40:00'),
                                                                                                            (25, 6, 13, 2900, 2920, '对战胜利', '2025-07-03 14:00:00'),
                                                                                                            (26, 7, 13, 2800, 2780, '对战失败', '2025-07-03 14:00:00'),
                                                                                                            (27, 8, 14, 2700, 2720, '对战胜利', '2025-07-03 15:35:00'),
                                                                                                            (28, 9, 14, 2600, 2580, '对战失败', '2025-07-03 15:35:00'),
                                                                                                            (29, 10, 15, 2500, 2520, '对战胜利', '2025-07-03 17:30:00'),
                                                                                                            (30, 11, 15, 2400, 2380, '对战失败', '2025-07-03 17:30:00'),
                                                                                                            (31, 12, 16, 2300, 2320, '对战胜利', '2025-07-04 09:25:00'),
                                                                                                            (32, 13, 16, 2200, 2180, '对战失败', '2025-07-04 09:25:00'),
                                                                                                            (33, 14, 17, 2100, 2120, '对战胜利', '2025-07-04 11:05:00'),
                                                                                                            (34, 15, 17, 2000, 1980, '对战失败', '2025-07-04 11:05:00'),
                                                                                                            (35, 16, 18, 1900, 1920, '对战胜利', '2025-07-04 14:50:00'),
                                                                                                            (36, 17, 18, 1800, 1780, '对战失败', '2025-07-04 14:50:00'),
                                                                                                            (37, 18, 19, 1700, 1720, '对战胜利', '2025-07-04 16:35:00'),
                                                                                                            (38, 19, 19, 1600, 1580, '对战失败', '2025-07-04 16:35:00'),
                                                                                                            (39, 20, 20, 2000, 2020, '对战胜利', '2025-07-04 18:45:00'),
                                                                                                            (40, 1, 20, 3480, 3460, '对战失败', '2025-07-04 18:45:00'),
                                                                                                            (41, 2, 21, 3420, 3440, '对战胜利', '2025-07-05 08:25:00'),
                                                                                                            (42, 3, 21, 3280, 3260, '对战失败', '2025-07-05 08:25:00'),
                                                                                                            (43, 4, 22, 3220, 3240, '对战胜利', '2025-07-05 10:40:00'),
                                                                                                            (44, 5, 22, 3080, 3060, '对战失败', '2025-07-05 10:40:00'),
                                                                                                            (45, 6, 23, 2920, 2940, '对战胜利', '2025-07-05 12:40:00'),
                                                                                                            (46, 7, 23, 2780, 2760, '对战失败', '2025-07-05 12:40:00'),
                                                                                                            (47, 8, 24, 2720, 2740, '对战胜利', '2025-07-05 16:20:00'),
                                                                                                            (48, 9, 24, 2580, 2560, '对战失败', '2025-07-05 16:20:00'),
                                                                                                            (49, 10, 25, 2520, 2540, '对战胜利', '2025-07-05 17:45:00'),
                                                                                                            (50, 11, 25, 2380, 2360, '对战失败', '2025-07-05 17:45:00'),
                                                                                                            (51, 12, 26, 2320, 2340, '对战胜利', '2025-07-06 10:00:00'),
                                                                                                            (52, 13, 26, 2180, 2160, '对战失败', '2025-07-06 10:00:00'),
                                                                                                            (53, 14, 27, 2120, 2140, '对战胜利', '2025-07-06 11:40:00'),
                                                                                                            (54, 15, 27, 1980, 1960, '对战失败', '2025-07-06 11:40:00'),
                                                                                                            (55, 16, 28, 1920, 1940, '对战胜利', '2025-07-06 13:10:00'),
                                                                                                            (56, 17, 28, 1780, 1760, '对战失败', '2025-07-06 13:10:00'),
                                                                                                            (57, 18, 29, 1720, 1740, '对战胜利', '2025-07-06 15:10:00'),
                                                                                                            (58, 19, 29, 1580, 1560, '对战失败', '2025-07-06 15:10:00'),
                                                                                                            (59, 20, 30, 2020, 2040, '对战胜利', '2025-07-06 16:50:00'),
                                                                                                            (60, 1, 30, 3460, 3440, '对战失败', '2025-07-06 16:50:00'),
                                                                                                            (61, 2, 31, 3440, 3460, '对战胜利', '2025-07-07 08:30:00'),
                                                                                                            (62, 3, 31, 3260, 3240, '对战失败', '2025-07-07 08:30:00'),
                                                                                                            (63, 4, 32, 3240, 3260, '对战胜利', '2025-07-07 10:30:00'),
                                                                                                            (64, 5, 32, 3060, 3040, '对战失败', '2025-07-07 10:30:00'),
                                                                                                            (65, 6, 33, 2940, 2960, '对战胜利', '2025-07-07 13:15:00'),
                                                                                                            (66, 7, 33, 2760, 2740, '对战失败', '2025-07-07 13:15:00'),
                                                                                                            (67, 8, 34, 2740, 2760, '对战胜利', '2025-07-07 15:30:00'),
                                                                                                            (68, 9, 34, 2560, 2540, '对战失败', '2025-07-07 15:30:00'),
                                                                                                            (69, 10, 35, 2540, 2560, '对战胜利', '2025-07-07 17:20:00'),
                                                                                                            (70, 11, 35, 2360, 2340, '对战失败', '2025-07-07 17:20:00'),
                                                                                                            (71, 12, 36, 2340, 2360, '对战胜利', '2025-07-08 10:05:00'),
                                                                                                            (72, 13, 36, 2160, 2140, '对战失败', '2025-07-08 10:05:00'),
                                                                                                            (73, 14, 37, 2140, 2160, '对战胜利', '2025-07-08 11:50:00'),
                                                                                                            (74, 15, 37, 1960, 1940, '对战失败', '2025-07-08 11:50:00'),
                                                                                                            (75, 16, 38, 1940, 1960, '对战胜利', '2025-07-08 13:45:00'),
                                                                                                            (76, 17, 38, 1760, 1740, '对战失败', '2025-07-08 13:45:00'),
                                                                                                            (77, 18, 39, 1740, 1760, '对战胜利', '2025-07-08 16:10:00'),
                                                                                                            (78, 19, 39, 1560, 1540, '对战失败', '2025-07-08 16:10:00'),
                                                                                                            (79, 20, 40, 2040, 2060, '对战胜利', '2025-07-08 17:40:00'),
                                                                                                            (80, 1, 40, 3440, 3420, '对战失败', '2025-07-08 17:40:00'),
                                                                                                            (81, 2, 41, 3460, 3480, '对战胜利', '2025-07-09 09:10:00'),
                                                                                                            (82, 3, 41, 3240, 3220, '对战失败', '2025-07-09 09:10:00'),
                                                                                                            (83, 4, 42, 3260, 3280, '对战胜利', '2025-07-09 10:45:00'),
                                                                                                            (84, 5, 42, 3040, 3020, '对战失败', '2025-07-09 10:45:00'),
                                                                                                            (85, 6, 43, 2960, 2980, '对战胜利', '2025-07-09 12:25:00'),
                                                                                                            (86, 7, 43, 2740, 2720, '对战失败', '2025-07-09 12:25:00'),
                                                                                                            (87, 8, 44, 2760, 2780, '对战胜利', '2025-07-09 14:25:00'),
                                                                                                            (88, 9, 44, 2540, 2520, '对战失败', '2025-07-09 14:25:00'),
                                                                                                            (89, 10, 45, 2560, 2580, '对战胜利', '2025-07-09 16:15:00'),
                                                                                                            (90, 11, 45, 2340, 2320, '对战失败', '2025-07-09 16:15:00'),
                                                                                                            (91, 12, 46, 2360, 2380, '对战胜利', '2025-07-10 09:20:00'),
                                                                                                            (92, 13, 46, 2140, 2120, '对战失败', '2025-07-10 09:20:00'),
                                                                                                            (93, 14, 47, 2160, 2180, '对战胜利', '2025-07-10 12:00:00'),
                                                                                                            (94, 15, 47, 1940, 1920, '对战失败', '2025-07-10 12:00:00'),
                                                                                                            (95, 16, 48, 1960, 1980, '对战胜利', '2025-07-10 14:15:00'),
                                                                                                            (96, 17, 48, 1740, 1720, '对战失败', '2025-07-10 14:15:00'),
                                                                                                            (97, 18, 49, 1760, 1780, '对战胜利', '2025-07-10 15:50:00'),
                                                                                                            (98, 19, 49, 1540, 1520, '对战失败', '2025-07-10 15:50:00'),
                                                                                                            (99, 20, 50, 2060, 2080, '对战胜利', '2025-07-10 17:25:00'),
                                                                                                            (100, 1, 50, 3420, 3400, '对战失败', '2025-07-10 17:25:00'),
                                                                                                            (101, 2, 51, 3480, 3500, '对战胜利', '2025-07-11 09:05:00'),
                                                                                                            (102, 3, 51, 3220, 3200, '对战失败', '2025-07-11 09:05:00'),
                                                                                                            (103, 4, 52, 3280, 3300, '对战胜利', '2025-07-11 11:20:00'),
                                                                                                            (104, 5, 52, 3020, 3000, '对战失败', '2025-07-11 11:20:00'),
                                                                                                            (105, 6, 53, 2980, 3000, '对战胜利', '2025-07-11 13:25:00'),
                                                                                                            (106, 7, 53, 2720, 2700, '对战失败', '2025-07-11 13:25:00'),
                                                                                                            (107, 8, 54, 2780, 2800, '对战胜利', '2025-07-11 15:00:00'),
                                                                                                            (108, 9, 54, 2520, 2500, '对战失败', '2025-07-11 15:00:00'),
                                                                                                            (109, 10, 55, 2580, 2600, '对战胜利', '2025-07-11 16:55:00'),
                                                                                                            (110, 11, 55, 2320, 2300, '对战失败', '2025-07-11 16:55:00'),
                                                                                                            (111, 12, 56, 2380, 2400, '对战胜利', '2025-07-12 09:35:00'),
                                                                                                            (112, 13, 56, 2120, 2100, '对战失败', '2025-07-12 09:35:00'),
                                                                                                            (113, 14, 57, 2180, 2200, '对战胜利', '2025-07-12 11:15:00'),
                                                                                                            (114, 15, 57, 1920, 1900, '对战失败', '2025-07-12 11:15:00'),
                                                                                                            (115, 16, 58, 1980, 2000, '对战胜利', '2025-07-12 13:55:00'),
                                                                                                            (116, 17, 58, 1720, 1700, '对战失败', '2025-07-12 13:55:00'),
                                                                                                            (117, 18, 59, 1780, 1800, '对战胜利', '2025-07-12 15:55:00'),
                                                                                                            (118, 19, 59, 1520, 1500, '对战失败', '2025-07-12 15:55:00'),
                                                                                                            (119, 20, 60, 2080, 2100, '对战胜利', '2025-07-12 17:40:00'),
                                                                                                            (120, 1, 60, 3400, 3380, '对战失败', '2025-07-12 17:40:00');

-- 插入帖子数据
INSERT INTO posts (id, user_id, title, content, is_top, post_time, update_time, is_deleted, like_count) VALUES
                                                                                                            (1, 1, '算法竞赛入门指南', '分享一些算法竞赛的入门经验和学习路径，希望对新手有帮助。主要包括基础数据结构、常用算法模板等内容。', 1, '2025-07-01 09:00:00', '2025-07-01 09:00:00', 0, 156),
                                                                                                            (2, 2, '动态规划经典题目总结', '整理了一些经典的动态规划题目，包括背包问题、最长公共子序列、最大子数组和等，附带详细解析。', 0, '2025-07-02 14:30:00', '2025-07-02 14:30:00', 0, 89),
                                                                                                            (3, 3, '图论算法学习笔记', '最近在学习图论算法，包括DFS、BFS、最短路径算法等，记录一下学习心得。', 0, '2025-07-03 16:45:00', '2025-07-03 16:45:00', 0, 67),
                                                                                                            (4, 5, '数据结构实现技巧', '分享一些常用数据结构的实现技巧，包括线段树、并查集、平衡树等高级数据结构。', 0, '2025-07-04 11:20:00', '2025-07-04 11:20:00', 0, 134),
                                                                                                            (5, 7, '比赛经验分享', '参加了很多算法竞赛，分享一些比赛中的经验和技巧，包括时间管理、调试技巧等。', 0, '2025-07-05 13:15:00', '2025-07-05 13:15:00', 0, 78),
                                                                                                            (6, 9, '字符串算法总结', 'KMP、Z算法、Manacher算法等字符串算法的总结，包含模板代码和应用场景。', 0, '2025-07-06 10:30:00', '2025-07-06 10:30:00', 0, 92),
                                                                                                            (7, 11, '数学在算法中的应用', '数论、组合数学、概率论等数学知识在算法竞赛中的应用，附带例题讲解。', 0, '2025-07-07 15:45:00', '2025-07-07 15:45:00', 0, 45),
                                                                                                            (8, 13, '贪心算法思路分析', '贪心算法的核心思想和常见应用场景，通过具体例题来理解贪心策略的选择。', 0, '2025-07-08 12:00:00', '2025-07-08 12:00:00', 0, 63),
                                                                                                            (9, 15, '二分查找的变种应用', '除了基础的二分查找，还有很多变种应用，如二分答案、三分查找等。', 0, '2025-07-09 14:20:00', '2025-07-09 14:20:00', 0, 71),
                                                                                                            (10, 17, '树形DP入门教程', '树形动态规划是DP的重要分支，通过几个经典例题来学习树形DP的基本思路。', 0, '2025-07-10 16:30:00', '2025-07-10 16:30:00', 0, 58),
                                                                                                            (11, 19, '新手常见错误总结', '作为一个刚入门的选手，总结一下自己犯过的常见错误，希望能帮助其他新手避坑。', 0, '2025-07-11 09:45:00', '2025-07-11 09:45:00', 0, 124),
                                                                                                            (12, 4, '分治算法详解', '分治算法的基本思想和经典应用，包括归并排序、快速排序、最近点对等问题。', 0, '2025-07-12 11:15:00', '2025-07-12 11:15:00', 0, 87);

-- 插入评论数据
INSERT INTO comments (id, post_id, user_id, parent_id, content, create_time, is_deleted) VALUES
                                                                                             (1, 1, 3, NULL, '非常好的入门指南！对新手很有帮助，感谢分享！', '2025-07-01 10:30:00', 0),
                                                                                             (2, 1, 5, NULL, '建议再加一些练习题目的推荐', '2025-07-01 11:15:00', 0),
                                                                                             (3, 1, 7, 2, '同意，有具体的题目练习会更好', '2025-07-01 12:00:00', 0),
                                                                                             (4, 2, 4, NULL, 'DP确实是算法竞赛的重点，这个总结很全面', '2025-07-02 15:45:00', 0),
                                                                                             (5, 2, 6, NULL, '背包问题的讲解很清楚，学到了！', '2025-07-02 16:30:00', 0),
                                                                                             (6, 3, 8, NULL, '图论算法确实比较难理解，感谢分享学习心得', '2025-07-03 17:20:00', 0),
                                                                                             (7, 3, 10, NULL, 'DFS和BFS的区别讲得很好', '2025-07-03 18:00:00', 0),
                                                                                             (8, 4, 2, NULL, '线段树的实现一直是我的难点，这个很有用', '2025-07-04 12:45:00', 0),
                                                                                             (9, 4, 12, NULL, '并查集的优化技巧很实用', '2025-07-04 13:30:00', 0),
                                                                                             (10, 5, 14, NULL, '比赛经验很宝贵，时间管理确实很重要', '2025-07-05 14:00:00', 0),
                                                                                             (11, 6, 16, NULL, 'KMP算法的模板代码很清晰', '2025-07-06 11:45:00', 0),
                                                                                             (12, 7, 18, NULL, '数学基础确实很重要，需要加强学习', '2025-07-07 16:20:00', 0),
                                                                                             (13, 8, 20, NULL, '贪心算法的关键是证明策略的正确性', '2025-07-08 13:15:00', 0),
                                                                                             (14, 9, 1, NULL, '二分答案是很有用的技巧', '2025-07-09 15:30:00', 0),
                                                                                             (15, 10, 3, NULL, '树形DP的状态转移需要仔细思考', '2025-07-10 17:45:00', 0),
                                                                                             (16, 11, 5, NULL, '新手确实容易犯这些错误，很有帮助', '2025-07-11 10:30:00', 0),
                                                                                             (17, 11, 7, 16, '我也犯过类似的错误，经验很重要', '2025-07-11 11:00:00', 0),
                                                                                             (18, 12, 9, NULL, '分治算法的递归思想很重要', '2025-07-12 12:30:00', 0);

-- 插入聊天消息数据
INSERT INTO chat_messages (id, user_id, content, sent_time, is_deleted) VALUES
                                                                            (1, 1, '大家好，欢迎来到CodeDuel！', '2025-07-01 08:00:00', 0),
                                                                            (2, 2, '这个平台很不错，可以和其他人一起刷题', '2025-07-01 08:15:00', 0),
                                                                            (3, 3, '刚刚完成了一场对战，感觉很刺激', '2025-07-01 10:20:00', 0),
                                                                            (4, 4, '有人想一起练习图论算法吗？', '2025-07-01 11:30:00', 0),
                                                                            (5, 5, '今天的题目难度适中，很有挑战性', '2025-07-01 14:30:00', 0),
                                                                            (6, 6, '求推荐一些动态规划的经典题目', '2025-07-01 15:45:00', 0),
                                                                            (7, 7, '刚刚看了那个入门指南的帖子，写得很好', '2025-07-01 16:20:00', 0),
                                                                            (8, 8, '有没有人想开房间一起刷题？', '2025-07-02 09:30:00', 0),
                                                                            (9, 9, '这个Rating系统设计得很合理', '2025-07-02 10:45:00', 0),
                                                                            (10, 10, '刚刚败给了一个高手，学到了很多', '2025-07-02 17:15:00', 0),
                                                                            (11, 11, '字符串算法真的很有趣', '2025-07-03 09:00:00', 0),
                                                                            (12, 12, '线段树的实现还是有点复杂', '2025-07-03 14:30:00', 0),
                                                                            (13, 13, '今天连胜了三场，心情很好', '2025-07-04 11:30:00', 0),
                                                                            (14, 14, '贪心算法的证明总是让我头疼', '2025-07-04 18:50:00', 0),
                                                                            (15, 15, '二分查找的边界处理需要小心', '2025-07-05 08:30:00', 0),
                                                                            (16, 16, '数学基础对算法竞赛真的很重要', '2025-07-05 12:45:00', 0),
                                                                            (17, 17, '树形DP的状态设计很关键', '2025-07-06 15:20:00', 0),
                                                                            (18, 18, '新手期间确实容易犯很多错误', '2025-07-07 10:15:00', 0),
                                                                            (19, 19, '感谢大家的帮助，学到了很多', '2025-07-08 16:40:00', 0),
                                                                            (20, 20, '管理员在此，有问题可以随时联系我', '2025-07-09 09:00:00', 0),
                                                                            (21, 1, '最近在研究一些新的算法优化技巧', '2025-07-10 14:20:00', 0),
                                                                            (22, 3, '分治算法在很多问题中都很有用', '2025-07-11 11:45:00', 0),
                                                                            (23, 5, '今天的对战质量都很高', '2025-07-12 13:30:00', 0),
                                                                            (24, 7, '希望能有更多有趣的题目', '2025-07-12 18:15:00', 0);

-- 数据插入完成
-- 总计：
-- 用户：20个
-- 题目：15个
-- 标签：12个
-- 题目标签关联：31个
-- 对战记录：60个
-- 用户对战记录关联：120个
-- 用户Rating历史：120个
-- 帖子：12个
-- 评论：18个
-- 聊天消息：24个
