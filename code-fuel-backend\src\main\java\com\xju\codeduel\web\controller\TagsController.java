package com.xju.codeduel.web.controller;

import com.xju.codeduel.common.JsonResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.xju.codeduel.common.JsonResponse;
import com.xju.codeduel.service.ITagsService;
import com.xju.codeduel.model.domain.Tags;


/**
 *
 *  前端控制器
 *
 *
 * <AUTHOR>
 * @since 2025-07-15
 * @version v1.0
 */
@RestController
@RequestMapping("/api/tags")
public class TagsController {

    private final Logger logger = LoggerFactory.getLogger( TagsController.class );

    @Autowired
    private ITagsService tagsService;


    /**
    * 描述：根据Id 查询
    *
    */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @ResponseBody
    public JsonResponse<Tags> getById(@PathVariable("id") Long id)throws Exception {
        Tags tags = tagsService.getById(id);
        return JsonResponse.success(tags);
    }
}

