package com.xju.codeduel.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户注册DTO
 * 用于接收前端发送的注册请求数据
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@ApiModel(value = "UserRegisterDTO", description = "用户注册请求数据")
public class UserRegisterDTO {

    @ApiModelProperty(value = "Codeforces用户名", required = true)
    private String codeforcesId;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "验证字符串", required = true)
    private String verificationString;

    public UserRegisterDTO() {}

    public UserRegisterDTO(String codeforcesId, String password, String verificationString) {
        this.codeforcesId = codeforcesId;
        this.password = password;
        this.verificationString = verificationString;
    }

    public String getCodeforcesId() {
        return codeforcesId;
    }

    public void setCodeforcesId(String codeforcesId) {
        this.codeforcesId = codeforcesId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getVerificationString() {
        return verificationString;
    }

    public void setVerificationString(String verificationString) {
        this.verificationString = verificationString;
    }

    @Override
    public String toString() {
        return "UserRegisterDTO{" +
                "codeforcesId='" + codeforcesId + '\'' +
                ", password='[PROTECTED]'" +
                ", verificationString='" + verificationString + '\'' +
                '}';
    }
}
