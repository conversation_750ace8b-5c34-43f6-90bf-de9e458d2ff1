spring:
  datasource:
    #    driver-class-name: com.mysql.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************
    username: root
    password: 123456
  mvc:
    format:
      date: yyyy-MM-dd
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  web:
    resources:
      static-locations: classpath:/META-INF/resources/, classpath:/resources/, classpath:/static/, classpath:/public/, file:${file-upload-path}
server:
  servlet:
    encoding:
      charset: utf-8
  port: 8080

mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml, classpath:/mybatis/mapper/extend/*.xml

logging:
  level:
    com.xju.codeduel.mapper: debug

file-upload-path: ./file